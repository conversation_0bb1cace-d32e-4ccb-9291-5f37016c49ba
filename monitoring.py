#!/usr/bin/env python3
"""
Monitoring Module for Web Scrapers

This module provides comprehensive monitoring capabilities for web scraping operations,
including:
- Request metrics collection
- Proxy performance monitoring
- Error rate tracking
- Alerting for threshold violations
- Reporting and dashboarding
"""

import os
import time
import json
import logging
import smtplib
import threading
import datetime
import statistics
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ult<PERSON>art
from typing import Dict, Any, List, Optional, Union, Callable
from functools import wraps
import traceback
from pathlib import Path

# Try importing optional dependencies
try:
    import psycopg2
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False
    logging.warning("PostgreSQL support not available. Install with 'pip install psycopg2-binary'")

try:
    from prometheus_client import Counter, Gauge, Histogram, Summary, start_http_server
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    logging.warning("Prometheus client not available. Install with 'pip install prometheus-client'")

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load configuration
def load_config():
    """Load monitoring configuration from file."""
    config = {
        "db": {
            "enabled": True,
            "type": "postgres",  # Only PostgreSQL is supported
            "host": "localhost",
            "name": "scraper_metrics",
            "user": "postgres",
            "password": "postgres"
        },
        "prometheus": {
            "enabled": PROMETHEUS_AVAILABLE,
            "port": 9090
        },
        "alert_thresholds": {
            "error_rate": 20,  # percentage
            "captcha_rate": 10,  # percentage
            "blocked_rate": 15,  # percentage
            "latency": 10  # seconds
        },
        "alert_channels": {
            "email": {
                "enabled": False,
                "smtp_server": "smtp.example.com",
                "smtp_port": 587,
                "use_tls": True,
                "username": "<EMAIL>",
                "password": "password",
                "from_address": "<EMAIL>",
                "recipients": ["<EMAIL>"]
            },
            "slack": {
                "enabled": False,
                "webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz"
            },
            "log": {
                "enabled": True
            }
        },
        "monitoring_interval": 60,  # seconds
        "proxy_health_check_interval": 300  # seconds
    }

    # Try loading from file
    try:
        with open('proxy_config.json', 'r') as f:
            file_config = json.load(f)

            # Update config with values from file
            if 'db' in file_config:
                config['db'].update(file_config['db'])

            if 'alert_thresholds' in file_config:
                config['alert_thresholds'].update(file_config['alert_thresholds'])

            if 'alert_channels' in file_config:
                for channel, settings in file_config['alert_channels'].items():
                    if channel in config['alert_channels']:
                        config['alert_channels'][channel].update(settings)
                    else:
                        config['alert_channels'][channel] = settings

            if 'prometheus' in file_config:
                config['prometheus'].update(file_config['prometheus'])

            if 'monitoring_interval' in file_config:
                config['monitoring_interval'] = file_config['monitoring_interval']

            if 'proxy_health_check_interval' in file_config:
                config['proxy_health_check_interval'] = file_config['proxy_health_check_interval']

            logger.info("Loaded monitoring config from proxy_config.json")
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.warning(f"Could not load monitoring config from file: {e}")

    return config

# Global config
CONFIG = load_config()

class DatabaseManager:
    """Database manager for metrics storage."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the database manager.

        Args:
            config: Database configuration
        """
        self.config = config
        self.connection = None
        self.initialized = False

        if not self.config['enabled']:
            logger.info("Database storage is disabled")
            return

        # Only PostgreSQL is supported
        if POSTGRES_AVAILABLE:
            self._init_postgres()
        else:
            logger.error("PostgreSQL support not available. Install with 'pip install psycopg2-binary'")
            self.config['enabled'] = False

    def _init_postgres(self):
        """Initialize PostgreSQL database."""
        try:
            # Check if PostgreSQL is running
            if self.config.get('skip_if_unavailable', True):
                # Try to connect with a short timeout
                import socket
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.settimeout(1)
                try:
                    host = self.config.get('host', 'localhost')
                    port = self.config.get('port', 5432)
                    s.connect((host, port))
                    s.close()
                except (socket.timeout, ConnectionRefusedError):
                    logger.warning(f"PostgreSQL server not available at {host}:{port}. Disabling database features.")
                    self.config['enabled'] = False
                    self.initialized = False
                    return

            self.connection = psycopg2.connect(
                host=self.config.get('host', 'localhost'),
                port=self.config.get('port', 5432),
                database=self.config.get('name', 'scraper_metrics'),
                user=self.config.get('user', 'postgres'),
                password=self.config.get('password', ''),
                connect_timeout=5  # Add a timeout to avoid hanging
            )
            self.connection.autocommit = True
            self._create_tables()
            self.initialized = True
            logger.info("Successfully initialized PostgreSQL database")
        except Exception as e:
            logger.warning(f"Error initializing PostgreSQL database: {e}")
            logger.info("Continuing without database support. Use --no-db flag to suppress this warning.")
            self.config['enabled'] = False
            self.initialized = False

    def _create_tables(self):
        """Create necessary database tables if they don't exist."""
        cursor = self.connection.cursor()

        # Create requests table
        try:
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS requests (
                    id SERIAL PRIMARY KEY,
                    scraper_name TEXT NOT NULL,
                    url TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status_code INTEGER,
                    success BOOLEAN NOT NULL,
                    response_time REAL,
                    proxy_id TEXT,
                    captcha_detected BOOLEAN,
                    error_message TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS proxy_metrics (
                    id SERIAL PRIMARY KEY,
                    proxy_id TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN NOT NULL,
                    response_time REAL,
                    status_code INTEGER,
                    error_message TEXT,
                    proxy_type TEXT DEFAULT 'http',
                    proxy_group TEXT DEFAULT 'default',
                    location TEXT DEFAULT 'unknown'
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    alert_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    message TEXT NOT NULL,
                    acknowledged BOOLEAN DEFAULT FALSE
                )
            ''')
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            traceback.print_exc()
            self.initialized = False
        finally:
            cursor.close()

    def store_request_metrics(self, metrics: Dict[str, Any]):
        """
        Store request metrics in the database.

        Args:
            metrics: Dictionary containing request metrics
        """
        if not self.config['enabled'] or not self.initialized:
            return

        try:
            cursor = self.connection.cursor()

            cursor.execute('''
                INSERT INTO requests
                (scraper_name, url, status_code, success, response_time, proxy_id, captcha_detected, error_message)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ''', (
                metrics.get('scraper_name', 'unknown'),
                metrics.get('url', ''),
                metrics.get('status_code'),
                metrics.get('success', False),
                metrics.get('response_time'),
                metrics.get('proxy_id'),
                metrics.get('captcha_detected', False),
                metrics.get('error_message')
            ))

            self.connection.commit()
        except Exception as e:
            logger.error(f"Error storing request metrics: {e}")
        finally:
            cursor.close()

    def store_proxy_metrics(self, metrics: Dict[str, Any]):
        """
        Store proxy metrics in the database.

        Args:
            metrics: Dictionary containing proxy metrics
        """
        if not self.config['enabled'] or not self.initialized:
            return

        try:
            cursor = self.connection.cursor()

            cursor.execute('''
                INSERT INTO proxy_metrics
                (proxy_id, success, response_time, status_code, error_message)
                VALUES (%s, %s, %s, %s, %s)
            ''', (
                metrics.get('proxy_id', ''),
                metrics.get('success', False),
                metrics.get('response_time'),
                metrics.get('status_code'),
                metrics.get('error_message')
            ))

            self.connection.commit()
        except Exception as e:
            logger.error(f"Error storing proxy metrics: {e}")
        finally:
            cursor.close()

    def store_alert(self, alert_type: str, severity: str, message: str):
        """
        Store an alert in the database.

        Args:
            alert_type: Type of alert
            severity: Alert severity (info, warning, error, critical)
            message: Alert message
        """
        if not self.config['enabled'] or not self.initialized:
            return

        try:
            cursor = self.connection.cursor()

            cursor.execute('''
                INSERT INTO alerts
                (alert_type, severity, message)
                VALUES (%s, %s, %s)
            ''', (alert_type, severity, message))

            self.connection.commit()
        except Exception as e:
            logger.error(f"Error storing alert: {e}")
        finally:
            cursor.close()

    def get_scraper_stats(self, scraper_name: str, hours: int = 24) -> Dict[str, Any]:
        """
        Get statistics for a scraper.

        Args:
            scraper_name: Name of the scraper
            hours: Number of hours to look back

        Returns:
            Dictionary with scraper statistics
        """
        if not self.config['enabled'] or not self.initialized:
            return {}

        try:
            cursor = self.connection.cursor()

            # Calculate timestamp for hours ago
            time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

            cursor.execute('''
                SELECT
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful_requests,
                    SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed_requests,
                    SUM(CASE WHEN captcha_detected = TRUE THEN 1 ELSE 0 END) as captcha_count,
                    AVG(response_time) as avg_response_time,
                    MIN(response_time) as min_response_time,
                    MAX(response_time) as max_response_time
                FROM requests
                WHERE scraper_name = %s AND timestamp > %s
            ''', (scraper_name, time_limit))

            result = cursor.fetchone()
            if result:
                total_requests, successful_requests, failed_requests, captcha_count, avg_response_time, min_response_time, max_response_time = result

                # Calculate error and captcha rates
                error_rate = 0
                if total_requests and total_requests > 0:
                    error_rate = (failed_requests / total_requests) * 100

                captcha_rate = 0
                if total_requests and total_requests > 0:
                    captcha_rate = (captcha_count / total_requests) * 100

                stats = {
                    'scraper_name': scraper_name,
                    'time_window_hours': hours,
                    'total_requests': total_requests,
                    'successful_requests': successful_requests,
                    'failed_requests': failed_requests,
                    'error_rate': error_rate,
                    'captcha_count': captcha_count,
                    'captcha_rate': captcha_rate,
                    'avg_response_time': avg_response_time,
                    'min_response_time': min_response_time,
                    'max_response_time': max_response_time,
                }

                return stats
            else:
                return {
                    'scraper_name': scraper_name,
                    'time_window_hours': hours,
                    'total_requests': 0,
                }

        except Exception as e:
            logger.error(f"Error getting scraper stats: {e}")
            return {}
        finally:
            cursor.close()

    def get_proxy_stats(self, proxy_id: str = None, hours: int = 24) -> Dict[str, Any]:
        """
        Get statistics for a proxy or all proxies.

        Args:
            proxy_id: ID of the proxy or None for all proxies
            hours: Number of hours to look back

        Returns:
            Dictionary with proxy statistics
        """
        if not self.config['enabled'] or not self.initialized:
            return {}

        try:
            cursor = self.connection.cursor()

            # Calculate timestamp for hours ago
            time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)
            stats = {}

            if proxy_id:
                # Get stats for specific proxy
                cursor.execute('''
                    SELECT
                        COUNT(*) as total_requests,
                        SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful_requests,
                        SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed_requests,
                        AVG(response_time) as avg_response_time,
                        MIN(response_time) as min_response_time,
                        MAX(response_time) as max_response_time
                    FROM proxy_metrics
                    WHERE proxy_id = %s AND timestamp > %s
                ''', (proxy_id, time_limit))

                result = cursor.fetchone()
                if result:
                    total_requests, successful_requests, failed_requests, avg_response_time, min_response_time, max_response_time = result

                    # Calculate success rate
                    success_rate = 0
                    if total_requests and total_requests > 0:
                        success_rate = (successful_requests / total_requests) * 100

                    stats[proxy_id] = {
                        'total_requests': total_requests,
                        'successful_requests': successful_requests,
                        'failed_requests': failed_requests,
                        'success_rate': success_rate,
                        'avg_response_time': avg_response_time,
                        'min_response_time': min_response_time,
                        'max_response_time': max_response_time,
                    }
            else:
                # Get stats for all proxies
                cursor.execute('''
                    SELECT
                        proxy_id,
                        COUNT(*) as total_requests,
                        SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful_requests,
                        SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed_requests,
                        AVG(response_time) as avg_response_time,
                        MIN(response_time) as min_response_time,
                        MAX(response_time) as max_response_time
                    FROM proxy_metrics
                    WHERE timestamp > %s
                    GROUP BY proxy_id
                ''', (time_limit,))

                results = cursor.fetchall()
                for result in results:
                    proxy_id, total_requests, successful_requests, failed_requests, avg_response_time, min_response_time, max_response_time = result

                    # Calculate success rate
                    success_rate = 0
                    if total_requests and total_requests > 0:
                        success_rate = (successful_requests / total_requests) * 100

                    stats[proxy_id] = {
                        'total_requests': total_requests,
                        'successful_requests': successful_requests,
                        'failed_requests': failed_requests,
                        'success_rate': success_rate,
                        'avg_response_time': avg_response_time,
                        'min_response_time': min_response_time,
                        'max_response_time': max_response_time,
                    }

            return stats

        except Exception as e:
            logger.error(f"Error getting proxy stats: {e}")
            return {}
        finally:
            cursor.close()

class AlertManager:
    """Manager for sending alerts for threshold violations."""

    def __init__(self, config: Dict[str, Any], db_manager: DatabaseManager):
        """
        Initialize the alert manager.

        Args:
            config: Alert configuration
            db_manager: Database manager instance
        """
        self.config = config
        self.db_manager = db_manager
        self.thresholds = self.config.get('alert_thresholds', {})
        self.channels = self.config.get('alert_channels', {})

        # Tracking sent alerts to prevent alert storms
        self.sent_alerts = {}
        self.alert_cooldown = 3600  # 1 hour cooldown for similar alerts

    def send_alert(self, alert_type: str, severity: str, message: str, details: Dict[str, Any] = None):
        """
        Send an alert through configured channels.

        Args:
            alert_type: Type of alert
            severity: Alert severity (info, warning, error, critical)
            message: Alert message
            details: Additional alert details
        """
        # Generate alert key for cooldown tracking
        alert_key = f"{alert_type}:{severity}:{message}"
        current_time = time.time()

        # Check if we've sent this alert recently
        if alert_key in self.sent_alerts:
            if current_time - self.sent_alerts[alert_key] < self.alert_cooldown:
                logger.debug(f"Skipping duplicate alert: {alert_key} (in cooldown period)")
                return

        # Update alert timestamp
        self.sent_alerts[alert_key] = current_time

        # Store alert in database
        if self.db_manager:
            self.db_manager.store_alert(alert_type, severity, message)

        # Format alert with details
        alert_text = f"[{severity.upper()}] {message}"
        if details:
            alert_text += "\n\nDetails:\n"
            for key, value in details.items():
                alert_text += f"- {key}: {value}\n"

        # Send through log channel
        if self.channels.get('log', {}).get('enabled', True):
            if severity == 'critical':
                logger.critical(alert_text)
            elif severity == 'error':
                logger.error(alert_text)
            elif severity == 'warning':
                logger.warning(alert_text)
            else:
                logger.info(alert_text)

        # Send through email channel
        if self.channels.get('email', {}).get('enabled', False):
            self._send_email_alert(alert_text, severity)

        # Send through Slack channel
        if self.channels.get('slack', {}).get('enabled', False):
            self._send_slack_alert(alert_text, severity)

    def _send_email_alert(self, alert_text: str, severity: str):
        """
        Send an alert via email.

        Args:
            alert_text: Formatted alert text
            severity: Alert severity
        """
        try:
            email_config = self.channels.get('email', {})

            # Create message
            msg = MIMEMultipart()
            msg['From'] = email_config.get('from_address')
            msg['To'] = ', '.join(email_config.get('recipients', []))
            msg['Subject'] = f"[ScraperAlert] {severity.upper()}: {alert_text.splitlines()[0][:50]}..."

            msg.attach(MIMEText(alert_text))

            # Connect to SMTP server
            server = smtplib.SMTP(
                email_config.get('smtp_server'),
                email_config.get('smtp_port', 587)
            )

            if email_config.get('use_tls', True):
                server.starttls()

            server.login(
                email_config.get('username'),
                email_config.get('password')
            )

            # Send email
            server.send_message(msg)
            server.quit()

            logger.info(f"Sent email alert to {len(email_config.get('recipients', []))} recipients")
        except Exception as e:
            logger.error(f"Error sending email alert: {e}")

    def _send_slack_alert(self, alert_text: str, severity: str):
        """
        Send an alert via Slack webhook.

        Args:
            alert_text: Formatted alert text
            severity: Alert severity
        """
        if not REQUESTS_AVAILABLE:
            logger.error("Cannot send Slack alert: requests module not available")
            return

        try:
            slack_config = self.channels.get('slack', {})
            webhook_url = slack_config.get('webhook_url')

            # Set color based on severity
            color = {
                'info': '#36a64f',
                'warning': '#ffcc00',
                'error': '#ff9900',
                'critical': '#ff0000'
            }.get(severity, '#eeeeee')

            # Prepare payload
            payload = {
                'attachments': [
                    {
                        'fallback': alert_text,
                        'color': color,
                        'title': f"ScraperAlert: {severity.upper()}",
                        'text': alert_text,
                        'footer': 'Trend Crawler Monitoring',
                        'ts': int(time.time())
                    }
                ]
            }

            # Send to webhook
            response = requests.post(
                webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code != 200:
                logger.error(f"Error sending Slack alert: {response.status_code} - {response.text}")
            else:
                logger.info("Sent Slack alert successfully")

        except Exception as e:
            logger.error(f"Error sending Slack alert: {e}")

    def check_scraper_thresholds(self, scraper_name: str):
        """
        Check if scraper metrics violate any thresholds.

        Args:
            scraper_name: Name of the scraper to check
        """
        # Get scraper stats
        stats = self.db_manager.get_scraper_stats(scraper_name, hours=1)  # Check last hour

        if not stats:
            return

        # Check error rate
        error_threshold = self.thresholds.get('error_rate')
        if error_threshold is not None and stats.get('error_rate', 0) > error_threshold:
            self.send_alert(
                'high_error_rate',
                'warning' if stats.get('error_rate', 0) < error_threshold * 1.5 else 'error',
                f"High error rate for scraper {scraper_name}: {stats.get('error_rate', 0):.1f}% (threshold: {error_threshold}%)",
                details=stats
            )

        # Check captcha rate
        captcha_threshold = self.thresholds.get('captcha_rate')
        if captcha_threshold is not None and stats.get('captcha_rate', 0) > captcha_threshold:
            self.send_alert(
                'high_captcha_rate',
                'warning',
                f"High CAPTCHA rate for scraper {scraper_name}: {stats.get('captcha_rate', 0):.1f}% (threshold: {captcha_threshold}%)",
                details=stats
            )

        # Check latency
        latency_threshold = self.thresholds.get('latency')
        if latency_threshold is not None and stats.get('avg_response_time', 0) > latency_threshold:
            self.send_alert(
                'high_latency',
                'info',
                f"High latency for scraper {scraper_name}: {stats.get('avg_response_time', 0):.2f}s (threshold: {latency_threshold}s)",
                details=stats
            )

class Monitor:
    """Main monitoring class for web scrapers."""

    def __init__(self):
        """Initialize the monitor."""
        self.config = CONFIG
        self.db_manager = DatabaseManager(self.config['db'])
        self.alert_manager = AlertManager(self.config, self.db_manager)
        self.scrapers = set()
        self.monitoring_active = False
        self.monitoring_thread = None
        self.proxy_health_check_interval = self.config.get('proxy_health_check_interval', 300)
        self.alerts = []
        self.alert_history_limit = 100  # Keep only the most recent 100 alerts
        self._lock = threading.Lock()
        self.connection = None
        self.initialized = False

        # Initialize database connection if enabled
        if self.config['db']['enabled'] and POSTGRES_AVAILABLE:
            try:
                self.connection = psycopg2.connect(
                    host=self.config['db'].get('host', 'localhost'),
                    port=self.config['db'].get('port', 5432),
                    database=self.config['db'].get('name', 'scraper_metrics'),
                    user=self.config['db'].get('user', 'postgres'),
                    password=self.config['db'].get('password', 'postgres123'),
                    connect_timeout=5
                )
                self.connection.autocommit = True
                self.initialized = True
                logger.info("Successfully initialized PostgreSQL connection in Monitor class")
            except Exception as e:
                logger.error(f"Error connecting to PostgreSQL in Monitor class: {e}")

        # Setup Prometheus metrics if available
        self.metrics = {}
        if PROMETHEUS_AVAILABLE and self.config.get('prometheus', {}).get('enabled', False):
            self._setup_prometheus()

    def _setup_prometheus(self):
        """Setup Prometheus metrics and server."""
        try:
            # Request metrics
            self.metrics['requests_total'] = Counter(
                'scraper_requests_total',
                'Total number of scraper requests',
                ['scraper_name', 'status']
            )

            self.metrics['request_duration'] = Histogram(
                'scraper_request_duration_seconds',
                'Request duration in seconds',
                ['scraper_name']
            )

            self.metrics['captcha_total'] = Counter(
                'scraper_captcha_total',
                'Total number of CAPTCHAs encountered',
                ['scraper_name']
            )

            # Proxy metrics
            self.metrics['proxy_requests_total'] = Counter(
                'proxy_requests_total',
                'Total number of proxy requests',
                ['proxy_id', 'status']
            )

            self.metrics['proxy_duration'] = Histogram(
                'proxy_request_duration_seconds',
                'Proxy request duration in seconds',
                ['proxy_id']
            )

            # Start metrics server
            prometheus_port = self.config.get('prometheus', {}).get('port', 9090)
            try:
                start_http_server(prometheus_port)
                logger.info(f"Started Prometheus metrics server on port {prometheus_port}")
            except OSError as e:
                if "Address already in use" in str(e):
                    # Try alternative ports
                    for alt_port in [9091, 9092, 9093]:
                        try:
                            start_http_server(alt_port)
                            logger.info(f"Started Prometheus metrics server on alternative port {alt_port}")
                            break
                        except OSError:
                            continue
                    else:
                        logger.warning("Could not start Prometheus metrics server - all ports in use")
                        self.config['prometheus']['enabled'] = False
                else:
                    logger.error(f"Error starting Prometheus metrics server: {e}")
                    self.config['prometheus']['enabled'] = False
        except Exception as e:
            logger.error(f"Error setting up Prometheus metrics: {e}")
            self.config['prometheus']['enabled'] = False

    def start_monitoring(self):
        """Start the monitoring system."""
        if self.monitoring_active:
            logger.warning("Monitoring is already active")
            return

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        logger.info("Started monitoring system")

    def stop_monitoring(self):
        """Stop the monitoring system."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5.0)
            logger.info("Stopped monitoring system")

    def _monitoring_loop(self):
        """Main monitoring loop."""
        try:
            while self.monitoring_active:
                # Check thresholds for all registered scrapers
                for scraper_name in self.scrapers:
                    self.alert_manager.check_scraper_thresholds(scraper_name)

                # Sleep for specified interval
                time.sleep(self.config.get('monitoring_interval', 60))
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
            traceback.print_exc()

    def register_scraper(self, scraper_name: str):
        """
        Register a scraper for monitoring.

        Args:
            scraper_name: Name of the scraper
        """
        self.scrapers.add(scraper_name)
        logger.info(f"Registered scraper: {scraper_name}")

    def record_request(self, scraper_name: str, metrics: Dict[str, Any]):
        """
        Record metrics for a scraper request.

        Args:
            scraper_name: Name of the scraper
            metrics: Dictionary with request metrics
        """
        # Register scraper if not already registered
        self.register_scraper(scraper_name)

        # Add scraper name to metrics
        metrics['scraper_name'] = scraper_name

        # Store metrics in database
        if self.db_manager:
            self.db_manager.store_request_metrics(metrics)

        # Record Prometheus metrics if available
        if PROMETHEUS_AVAILABLE and self.config.get('prometheus', {}).get('enabled', False):
            try:
                status = 'success' if metrics.get('success', False) else 'failure'
                self.metrics['requests_total'].labels(scraper_name, status).inc()

                if metrics.get('response_time'):
                    self.metrics['request_duration'].labels(scraper_name).observe(metrics['response_time'])

                if metrics.get('captcha_detected', False):
                    self.metrics['captcha_total'].labels(scraper_name).inc()
            except Exception as e:
                logger.error(f"Error recording Prometheus metrics: {e}")

    def record_proxy_request(self, proxy_id: str, metrics: Dict[str, Any]):
        """
        Record metrics for a proxy request.

        Args:
            proxy_id: ID of the proxy
            metrics: Dictionary with proxy metrics
        """
        # Add proxy ID to metrics
        metrics['proxy_id'] = proxy_id

        # Store metrics in database
        if self.db_manager:
            self.db_manager.store_proxy_metrics(metrics)

        # Record Prometheus metrics if available
        if PROMETHEUS_AVAILABLE and self.config.get('prometheus', {}).get('enabled', False):
            try:
                status = 'success' if metrics.get('success', False) else 'failure'
                self.metrics['proxy_requests_total'].labels(proxy_id, status).inc()

                if metrics.get('response_time'):
                    self.metrics['proxy_duration'].labels(proxy_id).observe(metrics['response_time'])
            except Exception as e:
                logger.error(f"Error recording Prometheus proxy metrics: {e}")

    def get_scraper_stats(self, scraper_name: str, hours: int = 24) -> Dict[str, Any]:
        """
        Get statistics for a scraper.

        Args:
            scraper_name: Name of the scraper
            hours: Number of hours to look back

        Returns:
            Dictionary with scraper statistics
        """
        if self.db_manager:
            return self.db_manager.get_scraper_stats(scraper_name, hours)
        return {}

    def get_proxy_stats(self, proxy_id: str = None, hours: int = 24) -> Dict[str, Any]:
        """
        Get statistics for a proxy or all proxies.

        Args:
            proxy_id: ID of the proxy or None for all proxies
            hours: Number of hours to look back

        Returns:
            Dictionary with proxy statistics
        """
        if self.db_manager:
            return self.db_manager.get_proxy_stats(proxy_id, hours)
        return {}

    def set_alert_threshold(self, metric: str, threshold: float, channel: str = None):
        """
        Set a threshold for alerting.

        Args:
            metric: Metric to set threshold for
            threshold: Threshold value
            channel: Alert channel to use (None for all channels)
        """
        self.config['alert_thresholds'][metric] = threshold
        logger.info(f"Set alert threshold for {metric}: {threshold}")

        if channel:
            # Make sure channel is enabled
            if channel in self.config['alert_channels']:
                self.config['alert_channels'][channel]['enabled'] = True
                logger.info(f"Enabled alert channel: {channel}")

    def send_alert(self, alert_type: str, severity: str, message: str, details: Dict[str, Any] = None):
        """
        Send an alert.

        Args:
            alert_type: Type of alert
            severity: Alert severity (info, warning, error, critical)
            message: Alert message
            details: Additional alert details
        """
        self.alert_manager.send_alert(alert_type, severity, message, details)

    def send_alert(self, alert_type, severity, message, details=None):
        """Send an alert and store it in history."""
        alert = {
            'timestamp': datetime.datetime.now().isoformat(),
            'type': alert_type,
            'severity': severity,
            'message': message,
            'details': details or {}
        }

        with self._lock:
            self.alerts.append(alert)
            # Maintain limited history
            if len(self.alerts) > self.alert_history_limit:
                self.alerts = self.alerts[-self.alert_history_limit:]

        return alert

    def get_recent_alerts(self, limit=3):
        """Get the most recent alerts."""
        with self._lock:
            # Return most recent alerts first (reversed)
            return sorted(self.alerts,
                         key=lambda x: x['timestamp'],
                         reverse=True)[:limit]

    def get_system_info(self) -> Dict[str, Any]:
        """
        Get system information including CPU, memory, disk usage, etc.

        Returns:
            Dictionary with system information
        """
        try:
            import psutil
            import platform
            from datetime import datetime

            # Get CPU info - use shorter interval for faster response
            cpu_percent = psutil.cpu_percent(interval=0.1)

            # Get memory info
            memory = psutil.virtual_memory()
            memory_used = round(memory.used / (1024 * 1024))
            memory_total = round(memory.total / (1024 * 1024))
            memory_percent = memory.percent

            # Get disk info
            disk = psutil.disk_usage('/')
            disk_used = round(disk.used / (1024 * 1024 * 1024))
            disk_total = round(disk.total / (1024 * 1024 * 1024))
            disk_percent = disk.percent

            # Get system uptime
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            uptime_str = f"up {uptime.days} days {uptime.seconds // 3600}:{(uptime.seconds // 60) % 60:02d}"

            # Get load average
            load_avg = psutil.getloadavg()

            # Format system info
            system_info = {
                "hostname": platform.node(),
                "cpu_usage": f"~{cpu_percent}%",
                "memory_usage": f"{memory_used}/{memory_total}MB (~{memory_percent}%)",
                "disk_usage": f"{disk_used}/{disk_total}GB (~{disk_percent}%)",
                "os_info": f"{platform.system()} {platform.release()} {platform.version()}",
                "kernel": platform.platform(),
                "cpu_info": platform.processor(),
                "python_version": platform.python_version(),
                "uptime": uptime_str,
                "load_avg": f"{load_avg[0]:.2f}, {load_avg[1]:.2f}, {load_avg[2]:.2f}"
            }

            return system_info
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {}

    def get_system_resources(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get historical system resource usage (CPU, memory, disk).

        Args:
            hours: Number of hours to look back

        Returns:
            Dictionary with system resource data
        """
        try:
            if not self.config['enabled'] or not self.initialized:
                return {"labels": [], "cpu_usage": [], "memory_usage": [], "disk_usage": []}

            cursor = self.connection.cursor()

            # Calculate timestamp for hours ago
            time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

            cursor.execute('''
                SELECT
                    timestamp,
                    cpu_usage,
                    memory_usage,
                    disk_usage
                FROM system_metrics
                WHERE timestamp > %s
                ORDER BY timestamp ASC
            ''', (time_limit,))

            results = cursor.fetchall()

            labels = []
            cpu_usage = []
            memory_usage = []
            disk_usage = []

            for result in results:
                timestamp, cpu, memory, disk = result
                labels.append(timestamp.isoformat())
                cpu_usage.append(cpu)
                memory_usage.append(memory)
                disk_usage.append(disk)

            return {
                "labels": labels,
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "disk_usage": disk_usage
            }

        except Exception as e:
            logger.error(f"Error getting system resources: {e}")
            return {"labels": [], "cpu_usage": [], "memory_usage": [], "disk_usage": []}

    def get_api_health(self) -> Dict[str, Any]:
        """
        Get API service health statistics.

        Returns:
            Dictionary with API health data
        """
        try:
            if not self.config['enabled'] or not self.initialized:
                return {"api_names": [], "response_times": [], "success_rates": []}

            cursor = self.connection.cursor()

            cursor.execute('''
                SELECT
                    api_name,
                    AVG(response_time) as avg_response_time,
                    COUNT(*) FILTER (WHERE success = TRUE) as success_count,
                    COUNT(*) FILTER (WHERE success = FALSE) as error_count
                FROM api_metrics
                WHERE timestamp > NOW() - INTERVAL '24 hours'
                GROUP BY api_name
            ''')

            results = cursor.fetchall()

            api_names = []
            response_times = []
            success_rates = []

            for result in results:
                api_name, avg_response_time, success_count, error_count = result

                api_names.append(api_name)
                response_times.append(avg_response_time)

                # Calculate success rate
                total_requests = success_count + error_count
                success_rate = 0
                if total_requests > 0:
                    success_rate = (success_count / total_requests) * 100
                success_rates.append(success_rate)

            return {
                "api_names": api_names,
                "response_times": response_times,
                "success_rates": success_rates
            }

        except Exception as e:
            logger.error(f"Error getting API health: {e}")
            return {"api_names": [], "response_times": [], "success_rates": []}

    def get_db_performance(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get database performance metrics.

        Args:
            hours: Number of hours to look back

        Returns:
            Dictionary with DB performance data
        """
        try:
            if not self.config['enabled'] or not self.initialized:
                return {"timestamps": [], "query_times": [], "active_connections": []}

            cursor = self.connection.cursor()

            # Calculate timestamp for hours ago
            time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

            cursor.execute('''
                SELECT
                    timestamp,
                    query_time,
                    active_connections
                FROM db_performance_metrics
                WHERE timestamp > %s
                ORDER BY timestamp ASC
            ''', (time_limit,))

            results = cursor.fetchall()

            timestamps = []
            query_times = []
            active_connections = []

            for result in results:
                timestamp, query_time, connections = result
                timestamps.append(timestamp.isoformat())
                query_times.append(query_time)
                active_connections.append(connections)

            return {
                "timestamps": timestamps,
                "query_times": query_times,
                "active_connections": active_connections
            }

        except Exception as e:
            logger.error(f"Error getting DB performance: {e}")
            return {"timestamps": [], "query_times": [], "active_connections": []}

    def get_system_events(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent system events.

        Args:
            limit: Maximum number of events to return

        Returns:
            List of system events
        """
        try:
            if not self.config['enabled'] or not self.initialized:
                return []

            cursor = self.connection.cursor()

            cursor.execute('''
                SELECT
                    timestamp,
                    event_type,
                    event_message
                FROM system_events
                ORDER BY timestamp DESC
                LIMIT %s
            ''', (limit,))

            results = cursor.fetchall()

            events = []

            for result in results:
                timestamp, event_type, event_message = result

                now = datetime.datetime.now()

                # Format timestamp for display
                if timestamp.date() == now.date():
                    time_str = f"Today {timestamp.strftime('%H:%M')}"
                elif timestamp.date() == (now - datetime.timedelta(days=1)).date():
                    time_str = f"Yesterday {timestamp.strftime('%H:%M')}"
                else:
                    time_str = timestamp.strftime('%Y-%m-%d %H:%M')

                events.append({
                    "time": time_str,
                    "type": event_type,
                    "message": event_message
                })

            return events

        except Exception as e:
            logger.error(f"Error getting system events: {e}")
            return []

    def add_proxy(self, ip: str, port: int, proxy_type: str, proxy_group: str,
                 username: Optional[str] = None, password: Optional[str] = None,
                 location: Optional[str] = None) -> bool:
        """
        Add a new proxy to the system.

        Args:
            ip: IP address of the proxy
            port: Port number
            proxy_type: Type of proxy (http, https, socks4, socks5)
            proxy_group: Group for categorizing proxies
            username: Optional authentication username
            password: Optional authentication password
            location: Optional location information

        Returns:
            True if proxy was added successfully
        """
        try:
            if not self.config['enabled'] or not self.initialized:
                return False

            cursor = self.connection.cursor()

            # Check if proxy already exists
            cursor.execute(
                "SELECT id FROM proxies WHERE ip_address = %s AND port = %s",
                (ip, port)
            )

            if cursor.fetchone():
                # Update existing proxy
                cursor.execute('''
                    UPDATE proxies SET
                        proxy_type = %s,
                        proxy_group = %s,
                        username = %s,
                        password = %s,
                        location = %s,
                        updated_at = %s
                    WHERE ip_address = %s AND port = %s
                ''', (
                    proxy_type,
                    proxy_group,
                    username,
                    password,
                    location,
                    datetime.datetime.now(),
                    ip,
                    port
                ))
            else:
                # Insert new proxy
                cursor.execute('''
                    INSERT INTO proxies (
                        ip_address, port, proxy_type, proxy_group,
                        username, password, location, status, created_at
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    ip,
                    port,
                    proxy_type,
                    proxy_group,
                    username,
                    password,
                    location,
                    'active',  # Default status
                    datetime.datetime.now()
                ))

            self.connection.commit()
            return True

        except Exception as e:
            logger.error(f"Error adding proxy: {e}")
            return False

    def get_system_info(self) -> Dict[str, Any]:
        """
        Get system information including CPU, memory, disk usage, etc.

        Returns:
            Dictionary with system information
        """
        try:
            import psutil
            import platform
            from datetime import datetime

            # Get CPU info - use shorter interval for faster response
            cpu_percent = psutil.cpu_percent(interval=0.1)

            # Get memory info
            memory = psutil.virtual_memory()
            memory_used = round(memory.used / (1024 * 1024))
            memory_total = round(memory.total / (1024 * 1024))
            memory_percent = memory.percent

            # Get disk info
            disk = psutil.disk_usage('/')
            disk_used = round(disk.used / (1024 * 1024 * 1024))
            disk_total = round(disk.total / (1024 * 1024 * 1024))
            disk_percent = disk.percent

            # Get system uptime
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            uptime_str = f"up {uptime.days} days {uptime.seconds // 3600}:{(uptime.seconds // 60) % 60:02d}"

            # Get load average
            load_avg = psutil.getloadavg()

            # Format system info
            system_info = {
                "hostname": platform.node(),
                "cpu_usage": f"{cpu_percent}%",
                "memory_usage": f"{memory_used}/{memory_total}MB ({memory_percent}%)",
                "disk_usage": f"{disk_used}/{disk_total}GB ({disk_percent}%)",
                "os_info": f"{platform.system()} {platform.release()} {platform.version()}",
                "kernel": platform.platform(),
                "cpu_info": platform.processor(),
                "python_version": platform.python_version(),
                "uptime": uptime_str,
                "load_avg": f"{load_avg[0]:.2f}, {load_avg[1]:.2f}, {load_avg[2]:.2f}"
            }

            return system_info
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {
                "hostname": "N/A",
                "cpu_usage": "N/A",
                "memory_usage": "N/A",
                "disk_usage": "N/A",
                "os_info": "N/A",
                "kernel": "N/A",
                "cpu_info": "N/A",
                "python_version": "N/A",
                "uptime": "N/A",
                "load_avg": "N/A"
            }

    def get_system_resources(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get historical system resource usage (CPU, memory, disk).

        Args:
            hours: Number of hours to look back

        Returns:
            Dictionary with system resource data
        """
        try:
            if not self.config['db']['enabled'] or not self.initialized or not self.connection:
                return {"labels": [], "cpu_usage": [], "memory_usage": [], "disk_usage": []}

            cursor = self.connection.cursor()

            # Calculate timestamp for hours ago
            time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

            cursor.execute('''
                SELECT
                    timestamp,
                    cpu_usage,
                    memory_usage,
                    disk_usage
                FROM system_metrics
                WHERE timestamp > %s
                ORDER BY timestamp ASC
            ''', (time_limit,))

            results = cursor.fetchall()
            cursor.close()

            labels = []
            cpu_usage = []
            memory_usage = []
            disk_usage = []

            for result in results:
                timestamp, cpu, memory, disk = result
                labels.append(timestamp.strftime('%H:%M:%S'))
                cpu_usage.append(float(cpu))
                memory_usage.append(float(memory))
                disk_usage.append(float(disk))

            return {
                "labels": labels,
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "disk_usage": disk_usage
            }
        except Exception as e:
            logger.error(f"Error getting system resources: {e}")
            return {"labels": [], "cpu_usage": [], "memory_usage": [], "disk_usage": []}

    def get_api_health(self) -> Dict[str, Any]:
        """
        Get API service health statistics.

        Returns:
            Dictionary with API health metrics
        """
        try:
            if not self.config['db']['enabled'] or not self.initialized or not self.connection:
                return {
                    "uptime": "N/A",
                    "success_rate": "N/A",
                    "avg_response_time": "N/A",
                    "requests_per_minute": "N/A",
                    "error_rate": "N/A",
                    "endpoints": []
                }

            cursor = self.connection.cursor()

            # Calculate timestamp for 1 hour ago
            time_limit = datetime.datetime.now() - datetime.timedelta(hours=1)

            # Get API metrics from the last hour
            cursor.execute('''
                SELECT
                    endpoint,
                    AVG(response_time) as avg_response_time,
                    COUNT(*) as request_count,
                    SUM(CASE WHEN success=true THEN 1 ELSE 0 END) as success_count
                FROM api_metrics
                WHERE timestamp > %s
                GROUP BY endpoint
                ORDER BY request_count DESC
            ''', (time_limit,))

            results = cursor.fetchall()

            # Get total requests and success rate
            cursor.execute('''
                SELECT
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success=true THEN 1 ELSE 0 END) as total_success
                FROM api_metrics
                WHERE timestamp > %s
            ''', (time_limit,))

            total_result = cursor.fetchone()
            cursor.close()

            # Calculate metrics
            total_requests = total_result[0] if total_result and total_result[0] else 0
            total_success = total_result[1] if total_result and total_result[1] else 0

            if total_requests > 0:
                success_rate = (total_success / total_requests) * 100
                error_rate = 100 - success_rate
                requests_per_minute = total_requests / 60
            else:
                success_rate = 0
                error_rate = 0
                requests_per_minute = 0

            # Format endpoint data
            endpoints = []
            for result in results:
                endpoint, avg_response_time, request_count, success_count = result
                endpoint_success_rate = (success_count / request_count) * 100 if request_count > 0 else 0

                endpoints.append({
                    "endpoint": endpoint,
                    "avg_response_time": f"{avg_response_time:.2f} ms" if avg_response_time else "N/A",
                    "request_count": request_count,
                    "success_rate": f"{endpoint_success_rate:.1f}%"
                })

            return {
                "uptime": "100%",  # Placeholder - would need actual uptime tracking
                "success_rate": f"{success_rate:.1f}%",
                "avg_response_time": f"{total_result[0]:.2f} ms" if total_result and total_result[0] > 0 else "N/A",
                "requests_per_minute": f"{requests_per_minute:.1f}",
                "error_rate": f"{error_rate:.1f}%",
                "endpoints": endpoints
            }
        except Exception as e:
            logger.error(f"Error getting API health: {e}")
            return {
                "uptime": "N/A",
                "success_rate": "N/A",
                "avg_response_time": "N/A",
                "requests_per_minute": "N/A",
                "error_rate": "N/A",
                "endpoints": []
            }

    def get_db_performance(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get database performance metrics.

        Args:
            hours: Number of hours to look back

        Returns:
            Dictionary with database performance data
        """
        try:
            if not self.config['db']['enabled'] or not self.initialized or not self.connection:
                return {
                    "queries_per_minute": "N/A",
                    "avg_query_time": "N/A",
                    "success_rate": "N/A",
                    "query_types": []
                }

            cursor = self.connection.cursor()

            # Calculate timestamp for the specified hours ago
            time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

            # Get DB performance metrics grouped by query type
            cursor.execute('''
                SELECT
                    query_type,
                    AVG(query_duration) as avg_duration,
                    COUNT(*) as query_count,
                    SUM(CASE WHEN success=true THEN 1 ELSE 0 END) as success_count
                FROM db_performance
                WHERE timestamp > %s
                GROUP BY query_type
                ORDER BY query_count DESC
            ''', (time_limit,))

            results = cursor.fetchall()

            # Get total queries and success rate
            cursor.execute('''
                SELECT
                    COUNT(*) as total_queries,
                    SUM(CASE WHEN success=true THEN 1 ELSE 0 END) as total_success,
                    AVG(query_duration) as avg_duration
                FROM db_performance
                WHERE timestamp > %s
            ''', (time_limit,))

            total_result = cursor.fetchone()
            cursor.close()

            # Calculate metrics
            total_queries = total_result[0] if total_result and total_result[0] else 0
            total_success = total_result[1] if total_result and total_result[1] else 0
            avg_duration = total_result[2] if total_result and total_result[2] else 0

            if total_queries > 0:
                success_rate = (total_success / total_queries) * 100
                queries_per_minute = total_queries / (hours * 60)
            else:
                success_rate = 0
                queries_per_minute = 0

            # Format query type data
            query_types = []
            for result in results:
                query_type, avg_duration, query_count, success_count = result
                type_success_rate = (success_count / query_count) * 100 if query_count > 0 else 0

                query_types.append({
                    "type": query_type,
                    "avg_duration": f"{avg_duration:.2f} ms" if avg_duration else "N/A",
                    "query_count": query_count,
                    "success_rate": f"{type_success_rate:.1f}%"
                })

            return {
                "queries_per_minute": f"{queries_per_minute:.1f}",
                "avg_query_time": f"{avg_duration:.2f} ms" if avg_duration else "N/A",
                "success_rate": f"{success_rate:.1f}%",
                "query_types": query_types
            }
        except Exception as e:
            logger.error(f"Error getting DB performance: {e}")
            return {
                "queries_per_minute": "N/A",
                "avg_query_time": "N/A",
                "success_rate": "N/A",
                "query_types": []
            }

    def get_system_events(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent system events.

        Args:
            limit: Maximum number of events to return

        Returns:
            List of system events
        """
        try:
            if not self.config['db']['enabled'] or not self.initialized or not self.connection:
                return []

            cursor = self.connection.cursor()

            cursor.execute('''
                SELECT
                    timestamp,
                    event_type,
                    severity,
                    message,
                    details
                FROM system_events
                ORDER BY timestamp DESC
                LIMIT %s
            ''', (limit,))

            results = cursor.fetchall()
            cursor.close()

            events = []
            for result in results:
                timestamp, event_type, severity, message, details = result
                events.append({
                    "timestamp": timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    "event_type": event_type,
                    "severity": severity,
                    "message": message,
                    "details": details
                })

            return events
        except Exception as e:
            logger.error(f"Error getting system events: {e}")
            return []

    def add_proxy(self, proxy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new proxy to the system.

        Args:
            proxy_data: Dictionary with proxy details

        Returns:
            Dictionary with result of operation
        """
        try:
            if not self.config['db']['enabled'] or not self.initialized or not self.connection:
                return {"success": False, "message": "Database not available"}

            # Validate required fields
            required_fields = ["ip_address", "port", "proxy_type"]
            for field in required_fields:
                if field not in proxy_data:
                    return {"success": False, "message": f"Missing required field: {field}"}

            cursor = self.connection.cursor()

            # Check if proxy already exists
            cursor.execute('''
                SELECT id FROM proxies WHERE ip_address = %s AND port = %s
            ''', (proxy_data['ip_address'], proxy_data['port']))

            existing_proxy = cursor.fetchone()
            if existing_proxy:
                cursor.close()
                return {"success": False, "message": "Proxy already exists"}

            # Insert new proxy
            cursor.execute('''
                INSERT INTO proxies (
                    ip_address, port, proxy_type, username, password, proxy_group, location
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            ''', (
                proxy_data['ip_address'],
                proxy_data['port'],
                proxy_data['proxy_type'],
                proxy_data.get('username'),
                proxy_data.get('password'),
                proxy_data.get('proxy_group'),
                proxy_data.get('location')
            ))

            new_id = cursor.fetchone()[0]
            self.connection.commit()
            cursor.close()

            return {
                "success": True,
                "message": "Proxy added successfully",
                "id": new_id
            }
        except Exception as e:
            logger.error(f"Error adding proxy: {e}")
            return {"success": False, "message": f"Error: {str(e)}"}

# Create singleton instances
monitor = Monitor()

# Ensure the monitoring system starts automatically when imported
def start_monitoring_on_import():
    """Start monitoring system when module is imported."""
    monitor.start_monitoring()

# Automatically start monitoring when module is imported
# Comment this out if you want manual control of starting monitoring
start_monitoring_on_import()