{% extends "base.html" %}

{% block content %}
<div class="filters">
    <div class="form-group">
        <label for="captcha-type">CAPTCHA Type:</label>
        <select id="captcha-type" class="form-control">
            <option value="all">All Types</option>
            <option value="recaptcha_v2">reCAPTCHA v2</option>
            <option value="recaptcha_v2_invisible">Invisible reCAPTCHA</option>
            <option value="recaptcha_v3">reCAPTCHA v3</option>
            <option value="hcaptcha">hCaptcha</option>
            <option value="turnstile">Cloudflare Turnstile</option>
            <option value="image">Image CAPTCHA</option>
            <option value="text">Text CAPTCHA</option>
        </select>
    </div>
    <div class="form-group">
        <label for="time-range">Time Range:</label>
        <select id="time-range" class="form-control">
            <option value="24">Last 24 Hours</option>
            <option value="48">Last 48 Hours</option>
            <option value="168">Last 7 Days</option>
            <option value="720">Last 30 Days</option>
        </select>
    </div>
    <button id="apply-filters" class="btn">Apply Filters</button>
</div>

<div class="dashboard-grid">
    <div class="dashboard-card">
        <h2>Success Rate by CAPTCHA Type</h2>
        <div class="chart-container">
            <canvas id="captchaSuccessRateChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Solver Performance</h2>
        <div class="chart-container">
            <canvas id="solverPerformanceChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Detection Accuracy</h2>
        <div class="chart-container">
            <canvas id="detectionAccuracyChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Response Time</h2>
        <div class="chart-container">
            <canvas id="responseTimeChart"></canvas>
        </div>
    </div>
</div>

<div class="dashboard-grid">
    <div class="dashboard-card">
        <h2>CAPTCHA Detection Heuristics</h2>
        <div class="heuristics-table-container" id="heuristicsTable">
            <p>Loading heuristics data...</p>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>ML Model Performance</h2>
        <div class="ml-performance-container" id="mlPerformance">
            <p>Loading ML model performance data...</p>
        </div>
    </div>
</div>

<div class="dashboard-card">
    <h2>CAPTCHA Solution Data</h2>
    <div class="table-container" id="captchaTable">
        <p>Loading CAPTCHA solution data...</p>
    </div>
</div>

<!-- CAPTCHA Feedback Modal -->
<div id="feedbackModal" class="modal">
    <div class="modal-content">
        <span class="close-modal">&times;</span>
        <h2>CAPTCHA Solution Feedback</h2>
        <form id="captchaFeedbackForm">
            <input type="hidden" id="captchaId" name="captchaId">
            <div class="form-group">
                <label for="correctSolution">Correct Solution:</label>
                <input type="text" id="correctSolution" name="correctSolution" class="form-control">
            </div>
            <div class="form-group">
                <label for="feedbackType">Feedback Type:</label>
                <select id="feedbackType" name="feedbackType" class="form-control">
                    <option value="incorrect_solution">Incorrect Solution</option>
                    <option value="false_positive">False Positive</option>
                    <option value="false_negative">False Negative</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="form-group">
                <label for="comments">Comments:</label>
                <textarea id="comments" name="comments" class="form-control" rows="3"></textarea>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Submit Feedback</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block head %}
<style>
.heuristics-table-container table {
    width: 100%;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-good {
    background-color: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

.status-warning {
    background-color: rgba(243, 156, 18, 0.2);
    color: #f39c12;
}

.status-error {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.url-cell {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ml-models {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.ml-model-card {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 1rem;
}

.ml-model-card h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.metric-group {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
}

.metric-label {
    display: block;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.progress-bar {
    height: 6px;
    background-color: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
}

.status-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.status-icon.success {
    background-color: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

.status-icon.failure {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.action-btn, .feedback-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    cursor: pointer;
}

.error-message {
    color: #e74c3c;
    padding: 1rem;
    border-left: 3px solid #e74c3c;
    background-color: rgba(231, 76, 60, 0.1);
    border-radius: 3px;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: var(--primary-color);
    font-style: italic;
}

.feedback-btn {
    background-color: var(--secondary-color);
    margin-left: 0.25rem;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 500px;
}

.close-modal {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover,
.close-modal:focus {
    color: #333;
    text-decoration: none;
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const applyFiltersBtn = document.getElementById('apply-filters');
    applyFiltersBtn.addEventListener('click', loadCaptchaData);
    
    // Modal functionality
    const modal = document.getElementById('feedbackModal');
    const feedbackBtns = document.querySelectorAll('.feedback-btn');
    const closeModal = document.querySelector('.close-modal');
    const captchaIdInput = document.getElementById('captchaId');
    
    // Open feedback modal when feedback button is clicked
    feedbackBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const captchaId = this.dataset.captchaId;
            captchaIdInput.value = captchaId;
            modal.style.display = 'block';
        });
    });
    
    // Close modal when clicking the X
    closeModal.addEventListener('click', function() {
        modal.style.display = 'none';
    });
    
    // Close modal when clicking outside the modal
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    // Handle form submission
    const feedbackForm = document.getElementById('captchaFeedbackForm');
    feedbackForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const captchaId = captchaIdInput.value;
        const correctSolution = document.getElementById('correctSolution').value;
        const feedbackType = document.getElementById('feedbackType').value;
        const comments = document.getElementById('comments').value;
        
        // Create feedback data for submission
        const feedbackData = {
            captcha_id: captchaId,
            correct_solution: correctSolution,
            feedback_type: feedbackType,
            comments: comments
        };
        
        // Submit feedback to the API
        fetch('/api/v1/captchas/feedback', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(feedbackData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to submit feedback');
            }
            return response.json();
        })
        .then(data => {
            // Show success message
            alert('Thank you for your feedback! It will help improve our CAPTCHA detection.');
            
            // Reset form and close modal
            feedbackForm.reset();
            modal.style.display = 'none';
        })
        .catch(error => {
            console.error('Error submitting feedback:', error);
            alert('Error submitting feedback. Please try again.');
        });
    });
    
    // Initial data load
    loadCaptchaData();
    initCharts();
     function loadCaptchaData() {
        const captchaType = document.getElementById('captcha-type').value;
        const timeRange = document.getElementById('time-range').value;
        
        // Show loading state
        document.querySelectorAll('.chart-container').forEach(container => {
            container.innerHTML = '<div class="loading-spinner">Loading data...</div>';
        });
        
        // Fetch CAPTCHA stats with filters
        loadCaptchaStats(captchaType, timeRange);
        
        // Load detection stats
        loadDetectionStats();
        
        // Load CAPTCHA solution history
        loadCaptchaSolutionHistory(captchaType, timeRange);
        
        // Initialize/update charts
        initCharts(captchaType, timeRange);
    }
    
    function loadCaptchaStats(captchaType, timeRange) {
        const url = `/api/v1/captchas/stats?hours=${timeRange}${captchaType !== 'all' ? '&type=' + captchaType : ''}`;
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch CAPTCHA statistics');
                }
                return response.json();
            })
            .then(data => {
                // Update the charts with the real data
                updateChartsWithData(data);
            })
            .catch(error => {
                console.error('Error fetching CAPTCHA stats:', error);
                // Show error message in chart containers
                document.querySelectorAll('.chart-container').forEach(container => {
                    container.innerHTML = `<div class="error-message">Error loading data: ${error.message}</div>`;
                });
            });
    }
    
    function loadDetectionStats() {
        const fallbackHeuristics = {
            heuristics: [
                { name: "Image Analysis", detections: 0, success_rate: 0 },
                { name: "DOM Structure", detections: 0, success_rate: 0 },
                { name: "Network Traffic", detections: 0, success_rate: 0 }
            ],
            models: [
                { name: "reCAPTCHA v2 Model", accuracy: 0, avg_time: 0, solved: 0 },
                { name: "hCaptcha Model", accuracy: 0, avg_time: 0, solved: 0 },
                { name: "Image CAPTCHA Model", accuracy: 0, avg_time: 0, solved: 0 }
            ]
        };
        
        // Use our utilities for API calls with fallback
        fetchApiDataWithFallback('/api/v1/captchas/detection-stats', fallbackHeuristics, function(data) {
            // Update the heuristics table with real data or fallback
            updateHeuristicsTable(data);
            
            // Update ML model performance with real data or fallback
            updateMLPerformance(data);
            
            // Return the processed data
            return data;
        });
    }
    
    function loadCaptchaSolutionHistory(captchaType, timeRange) {
        const url = `/api/v1/captchas/feedback-history?hours=${timeRange}${captchaType !== 'all' ? '&type=' + captchaType : ''}`;
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch CAPTCHA solution history');
                }
                return response.json();
            })
            .then(data => {
                // Update the CAPTCHA solution table with real data
                updateCaptchaTable(data);
            })
            .catch(error => {
                console.error('Error fetching CAPTCHA solution history:', error);
                // Show error in the CAPTCHA table
                document.getElementById('captchaTable').innerHTML = 
                    `<div class="error-message">Error loading CAPTCHA solutions: ${error.message}</div>`;
            });
    }
    
    function updateHeuristicsTable(data) {
        const tableContainer = document.getElementById('heuristicsTable');
        if (!data || !data.heuristics || data.heuristics.length === 0) {
            tableContainer.innerHTML = '<p>No heuristic data available.</p>';
            return;
        }
        
        let tableHtml = `
            <table>
                <thead>
                    <tr>
                        <th>Heuristic</th>
                        <th>Detections</th>
                        <th>Success Rate</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        data.heuristics.forEach(item => {
            const statusClass = item.success_rate >= 90 ? 'status-good' : 
                               item.success_rate >= 75 ? 'status-warning' : 'status-error';
            
            const statusText = item.success_rate >= 90 ? 'Active' : 
                              item.success_rate >= 75 ? 'Improving' : 'Needs Review';
            
            tableHtml += `
                <tr>
                    <td>${item.name}</td>
                    <td>${item.detections.toLocaleString()}</td>
                    <td>${item.success_rate.toFixed(1)}%</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                </tr>
            `;
        });
        
        tableHtml += '</tbody></table>';
        tableContainer.innerHTML = tableHtml;
    }
    
    function updateMLPerformance(data) {
        const mlContainer = document.getElementById('mlPerformance');
        if (!data || !data.models || data.models.length === 0) {
            mlContainer.innerHTML = '<p>No ML model performance data available.</p>';
            return;
        }
        
        let modelsHtml = '<div class="ml-models">';
        
        data.models.forEach(model => {
            modelsHtml += `
                <div class="ml-model-card">
                    <h3>${model.name}</h3>
                    <div class="metric-group">
                        <div class="metric">
                            <span class="metric-value">${model.accuracy.toFixed(1)}%</span>
                            <span class="metric-label">Accuracy</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value">${model.avg_time.toFixed(2)}s</span>
                            <span class="metric-label">Avg. Time</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value">${model.solved.toLocaleString()}</span>
                            <span class="metric-label">Solved</span>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${model.accuracy}%;"></div>
                    </div>
                </div>
            `;
        });
        
        modelsHtml += '</div>';
        mlContainer.innerHTML = modelsHtml;
    }
    
    function updateCaptchaTable(data) {
        const tableContainer = document.getElementById('captchaTable');
        if (!data || !data.solutions || data.solutions.length === 0) {
            tableContainer.innerHTML = '<p>No CAPTCHA solution data available.</p>';
            return;
        }
        
        let tableHtml = `
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>URL</th>
                        <th>Solver</th>
                        <th>Success</th>
                        <th>Time (s)</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        data.solutions.forEach(item => {
            const statusIcon = item.success ? 
                '<span class="status-icon success">✓</span>' : 
                '<span class="status-icon failure">✗</span>';
            
            tableHtml += `
                <tr>
                    <td>${item.timestamp}</td>
                    <td>${item.type}</td>
                    <td class="url-cell">${item.url}</td>
                    <td>${item.solver}</td>
                    <td>${statusIcon}</td>
                    <td>${item.solve_time.toFixed(2)}</td>
                    <td>
                        <button class="action-btn" data-captcha-id="${item.captcha_id}">View</button>
                        <button class="feedback-btn" data-captcha-id="${item.captcha_id}">Feedback</button>
                    </td>
                </tr>
            `;
        });
        
        tableHtml += '</tbody></table>';
        tableContainer.innerHTML = tableHtml;
        
        // Re-attach event listeners to the new buttons
        document.querySelectorAll('.feedback-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const captchaId = this.dataset.captchaId;
                document.getElementById('captchaId').value = captchaId;
                document.getElementById('feedbackModal').style.display = 'block';
            });
        });
    }

    function initCharts(captchaType, timeRange) {
        // We'll update these charts with real data when it arrives
        initCaptchaSuccessRateChart();
        initSolverPerformanceChart();
        initDetectionAccuracyChart();
        initResponseTimeChart();
    }
    
    function initCaptchaSuccessRateChart() {
        try {
            // First safely destroy any existing chart
            if (window.captchaSuccessChart) {
                if (window.chartUtils && window.chartUtils.safeDestroyChart) {
                    window.chartUtils.safeDestroyChart(window.captchaSuccessChart);
                } else if (typeof window.captchaSuccessChart.destroy === 'function') {
                    window.captchaSuccessChart.destroy();
                }
            }
            
            const config = {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Success Rate (%)',
                        data: [],
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            };
            
            // Use chart utils if available, otherwise fall back to direct creation
            if (window.chartUtils) {
                // Generate fallback data in case the chart has no data
                const fallbackData = window.chartUtils.generateFallbackData('bar');
                window.captchaSuccessChart = window.chartUtils.initChartWithFallback(
                    'captchaSuccessChart',
                    'captchaSuccessRateChart',
                    config,
                    fallbackData
                );
            } else {
                const captchaSuccessCtx = document.getElementById('captchaSuccessRateChart').getContext('2d');
                window.captchaSuccessChart = new Chart(captchaSuccessCtx, config);
            }
        } catch (error) {
            console.error('Error initializing CAPTCHA success rate chart:', error);
            const container = document.getElementById('captchaSuccessRateChart').parentNode;
            container.innerHTML = `<div class="error-message">Error initializing chart: ${error.message}</div>`;
        }
    }
    
    function initSolverPerformanceChart() {
        try {
            // First safely destroy any existing chart
            if (window.solverPerformanceChart) {
                if (window.chartUtils && window.chartUtils.safeDestroyChart) {
                    window.chartUtils.safeDestroyChart(window.solverPerformanceChart);
                } else if (typeof window.solverPerformanceChart.destroy === 'function') {
                    window.solverPerformanceChart.destroy();
                }
            }
            
            const config = {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Success Rate (%)',
                        data: [],
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            };
            
            // Use chart utils if available, otherwise fall back to direct creation
            if (window.chartUtils) {
                // Generate fallback data in case the chart has no data
                const fallbackData = window.chartUtils.generateFallbackData('bar');
                window.solverPerformanceChart = window.chartUtils.initChartWithFallback(
                    'solverPerformanceChart', 
                    'solverPerformanceChart', 
                    config,
                    fallbackData
                );
            } else {
                const solverPerformanceCtx = document.getElementById('solverPerformanceChart').getContext('2d');
                window.solverPerformanceChart = new Chart(solverPerformanceCtx, config);
            }
        } catch (error) {
            console.error('Error initializing solver performance chart:', error);
            const container = document.getElementById('solverPerformanceChart').parentNode;
            container.innerHTML = `<div class="error-message">Error initializing chart: ${error.message}</div>`;
        }
    }
    
    function initDetectionAccuracyChart() {
        try {
            // First safely destroy any existing chart
            if (window.detectionAccuracyChart) {
                if (window.chartUtils && window.chartUtils.safeDestroyChart) {
                    window.chartUtils.safeDestroyChart(window.detectionAccuracyChart);
                } else if (typeof window.detectionAccuracyChart.destroy === 'function') {
                    window.detectionAccuracyChart.destroy();
                }
            }
            
            const config = {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Detection Accuracy (%)',
                        data: [],
                        borderColor: 'rgba(153, 102, 255, 1)',
                        backgroundColor: 'rgba(153, 102, 255, 0.2)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            };
            
            // Use chart utils if available, otherwise fall back to direct creation
            if (window.chartUtils) {
                // Generate fallback data in case the chart has no data
                const fallbackData = window.chartUtils.generateFallbackData('line');
                window.detectionAccuracyChart = window.chartUtils.initChartWithFallback(
                    'detectionAccuracyChart',
                    'detectionAccuracyChart',
                    config,
                    fallbackData
                );
            } else {
                const detectionAccuracyCtx = document.getElementById('detectionAccuracyChart').getContext('2d');
                window.detectionAccuracyChart = new Chart(detectionAccuracyCtx, config);
            }
        } catch (error) {
            console.error('Error initializing detection accuracy chart:', error);
            const container = document.getElementById('detectionAccuracyChart').parentNode;
            container.innerHTML = `<div class="error-message">Error initializing chart: ${error.message}</div>`;
        }
    }
    
    function initResponseTimeChart() {
        try {
            // First safely destroy any existing chart
            if (window.responseTimeChart) {
                if (window.chartUtils && window.chartUtils.safeDestroyChart) {
                    window.chartUtils.safeDestroyChart(window.responseTimeChart);
                } else if (typeof window.responseTimeChart.destroy === 'function') {
                    window.responseTimeChart.destroy();
                }
            }
            
            const config = {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Avg Response Time (s)',
                        data: [],
                        backgroundColor: 'rgba(255, 159, 64, 0.2)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            };
            
            // Use chart utils if available, otherwise fall back to direct creation
            if (window.chartUtils) {
                // Generate fallback data in case the chart has no data
                const fallbackData = window.chartUtils.generateFallbackData('bar');
                window.responseTimeChart = window.chartUtils.initChartWithFallback(
                    'responseTimeChart',
                    'responseTimeChart',
                    config,
                    fallbackData
                );
            } else {
                const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');
                window.responseTimeChart = new Chart(responseTimeCtx, config);
            }
        } catch (error) {
            console.error('Error initializing response time chart:', error);
            const container = document.getElementById('responseTimeChart').parentNode;
            container.innerHTML = `<div class="error-message">Error initializing chart: ${error.message}</div>`;
        }
    }
    
    function updateChartsWithData(data) {
        try {
            // Default to empty data if not provided
            data = data || {};
            
            // Update Success Rate by CAPTCHA Type chart
            if (window.captchaSuccessChart) {
                if (data.captcha_types && Array.isArray(data.captcha_types) && data.captcha_types.length > 0) {
                    window.captchaSuccessChart.data.labels = data.captcha_types.map(item => item.name);
                    window.captchaSuccessChart.data.datasets[0].data = data.captcha_types.map(item => item.success_rate);
                } else {
                    // Use fallback data if the real data is missing
                    const fallback = window.chartUtils ? 
                        window.chartUtils.generateFallbackData('bar', 5) : 
                        { labels: ['No Data Available'], data: [0] };
                    
                    window.captchaSuccessChart.data.labels = fallback.labels;
                    window.captchaSuccessChart.data.datasets[0].data = fallback.data;
                }
                window.captchaSuccessChart.update();
            }
            
            // Update Solver Performance chart
            if (window.solverPerformanceChart) {
                if (data.solvers && Array.isArray(data.solvers) && data.solvers.length > 0) {
                    window.solverPerformanceChart.data.labels = data.solvers.map(item => item.name);
                    window.solverPerformanceChart.data.datasets[0].data = data.solvers.map(item => item.success_rate);
                } else {
                    // Use fallback data if the real data is missing
                    const fallback = window.chartUtils ? 
                        window.chartUtils.generateFallbackData('bar', 3) : 
                        { labels: ['No Data Available'], data: [0] };
                    
                    window.solverPerformanceChart.data.labels = fallback.labels;
                    window.solverPerformanceChart.data.datasets[0].data = fallback.data;
                }
                window.solverPerformanceChart.update();
            }
            
            // Update Detection Accuracy chart
            if (window.detectionAccuracyChart) {
                if (data.detection_trend && Array.isArray(data.detection_trend) && data.detection_trend.length > 0) {
                    window.detectionAccuracyChart.data.labels = data.detection_trend.map(item => item.period);
                    window.detectionAccuracyChart.data.datasets[0].data = data.detection_trend.map(item => item.accuracy);
                } else {
                    // Use fallback data if the real data is missing
                    const fallback = window.chartUtils ? 
                        window.chartUtils.generateFallbackData('line', 7) : 
                        { labels: ['No Data Available'], data: [0] };
                    
                    window.detectionAccuracyChart.data.labels = fallback.labels;
                    window.detectionAccuracyChart.data.datasets[0].data = fallback.data;
                }
                window.detectionAccuracyChart.update();
            }
            
            // Update Response Time chart
            if (window.responseTimeChart) {
                if (data.solvers && Array.isArray(data.solvers) && data.solvers.length > 0) {
                    window.responseTimeChart.data.labels = data.solvers.map(item => item.name);
                    window.responseTimeChart.data.datasets[0].data = data.solvers.map(item => item.avg_time);
                } else {
                    // Use fallback data if the real data is missing
                    const fallback = window.chartUtils ? 
                        window.chartUtils.generateFallbackData('bar', 3) : 
                        { labels: ['No Data Available'], data: [0] };
                    
                    window.responseTimeChart.data.labels = fallback.labels;
                    window.responseTimeChart.data.datasets[0].data = fallback.data;
                }
                window.responseTimeChart.update();
            }
        } catch (error) {
            console.error('Error updating charts with data:', error);
        }
    }
});
</script>
{% endblock %}