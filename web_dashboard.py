#!/usr/bin/env python3
"""
Web Dashboard Module for Trend-Crawler

This module provides a web interface with dashboards and reports for the
trend-crawler system, including:
- Scraper performance metrics
- CAPTCHA detection statistics
- Proxy performance monitoring
- Alerts and notifications
- System health overview
- User authentication and management
"""

import os
import datetime
import logging
import warnings
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import secrets
from datetime import timedelta

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logging.warning("python-dotenv not available. Environment variables will be loaded from system only.")

# Suppress passlib bcrypt warnings
warnings.filterwarnings("ignore", category=UserWarning, module="passlib")

# Web framework
from fastapi import (
    FastAPI, APIRouter, HTTPException, Depends, Request, Form, status
)
from fastapi.responses import (
    HTMLResponse, JSONResponse, RedirectResponse, FileResponse
)
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import O<PERSON><PERSON>2P<PERSON><PERSON><PERSON>earer, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from passlib.context import CryptContext
from passlib.exc import UnknownHashError
from pydantic import BaseModel, Field

# Import monitoring system
try:
    from monitoring import monitor
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    logging.warning(
        "Monitoring module not available. Some features will be limited."
    )

# Import CAPTCHA solver
try:
    from captcha_solver import captcha_solver
    CAPTCHA_SOLVER_AVAILABLE = True
except ImportError:
    CAPTCHA_SOLVER_AVAILABLE = False
    logging.warning(
        "CAPTCHA solver not available. Some features will be limited."
    )

# Try importing PostgreSQL
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor, DictCursor
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False
    logging.warning("PostgreSQL support not available. Some features will be limited.")

# Add this to the imports section at the top of the file (around line 33)
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError as e:
    PSUTIL_AVAILABLE = False
    logging.error(
        "Failed to import 'psutil'. System monitoring features will be disabled. "
        "This usually means the package is not installed in your Python environment. "
        "Please ensure 'psutil' is listed in your 'requirements.txt' and install dependencies via "
        "'pip install -r requirements.txt' in your active 'trend-crawler' environment. "
        f"Original error: {e}"
    )

# Configure logging with enhanced security logging
import logging.handlers

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create a dedicated security logger that writes to a file
security_logger = logging.getLogger('security')
security_handler = logging.handlers.RotatingFileHandler(
    'dashboard.log', maxBytes=10*1024*1024, backupCount=5
)
security_handler.setFormatter(
    logging.Formatter('%(asctime)s - SECURITY - %(levelname)s - %(message)s')
)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)


def get_fallback_detection_stats():
    """Return fallback detection stats when CAPTCHA solver is not available."""
    return {
        "recaptcha_detected": 0,
        "hcaptcha_detected": 0,
        "cloudflare_detected": 0,
        "other_detected": 0,
        "total_detected": 0
    }


# Current year for templates (avoids using Django-specific {% now %} tag)
def get_current_year():
    """Return the current year as a string."""
    return str(datetime.datetime.now().year)

# JWT Settings
SECRET_KEY = os.environ.get("SECRET_KEY", secrets.token_hex(32))
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 8  # 8 hours

# Security settings
DEFAULT_ADMIN_EMAIL = os.environ.get("ADMIN_EMAIL", "<EMAIL>")
DEFAULT_ADMIN_FULLNAME = os.environ.get("ADMIN_FULLNAME", "Admin User")
DEFAULT_ADMIN_USERNAME = os.environ.get("ADMIN_USERNAME", "admin")
ENFORCE_PASSWORD_CHANGE = True  # Enforce password change on first login

# Store whether any password changes have been applied since startup
# This helps us track if first-login password change has occurred
ADMIN_FIRST_LOGIN = True

def generate_secure_password():
    """Generate a secure random password or use the one from environment.

    This ensures that when deployed in production, we can set a secure password
    via environment variables instead of hardcoding it.
    """
    # Check if password is provided via environment variable
    env_password = os.environ.get("ADMIN_PASSWORD")
    if (env_password):
        return env_password

    # Use 'admin' as the default password
    password = "admin"

    # Log that we're using the default password
    logger.warning("Using default admin password 'admin'. User will be forced to change on first login.")
    return password

# Password hasher - Updated configuration to remove bcrypt warnings
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="passlib")

pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__default_rounds=12,
    bcrypt__min_rounds=4,
    bcrypt__max_rounds=31
)
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/token")
oauth2_scheme_optional = OAuth2PasswordBearer(
    tokenUrl="api/v1/auth/token", auto_error=False
)

# User models
class User(BaseModel):
    """Base user model."""
    username: str
    email: str
    full_name: Optional[str] = None
    disabled: Optional[bool] = False
    is_admin: Optional[bool] = False

class UserInDB(User):
    """User model with password hash."""
    password_hash: str

class Token(BaseModel):
    """Token model for JWT authentication."""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Token data model."""
    username: str

class UserCreate(BaseModel):
    """User creation model."""
    username: str
    email: str
    password: str
    full_name: Optional[str] = None
    is_admin: Optional[bool] = False

class PasswordChange(BaseModel):
    """Password change model."""
    current_password: str
    new_password: str

class ProfileUpdate(BaseModel):
    """Profile update model with email validation."""
    email: str = Field(..., pattern=r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
    full_name: Optional[str] = None

# Proxy model for adding new proxies
class ProxyCreate(BaseModel):
    """Request model for creating a new proxy."""
    ip: str
    port: int
    type: str  # "http", "https", "socks4", "socks5"
    group: str  # "us", "eu", "asia", "residential", "datacenter"
    username: Optional[str] = None
    password: Optional[str] = None
    location: Optional[str] = None

# CAPTCHA Feedback model
class CaptchaFeedbackRequest(BaseModel):
    """Request model for CAPTCHA feedback."""
    captcha_id: str
    correct_solution: Optional[str] = None
    feedback_type: str
    comments: Optional[str] = None

# Integration Test model
class IntegrationTestRequest(BaseModel):
    """Request model for integration tests."""
    target_type: str  # "scraper", "proxy", "captcha_solver"
    target_name: str
    test_type: str  # "connectivity", "performance", "accuracy"
    params: Optional[Dict[str, Any]] = None

# Fallback in-memory database for when PostgreSQL is not available
USERS_DB = {}

# User database connection
def get_db_connection():
    """Get a database connection."""
    if not POSTGRES_AVAILABLE:
        raise Exception("PostgreSQL support not available")

    try:
        conn = psycopg2.connect(
            host=os.environ.get("DB_HOST", "localhost"),
            port=os.environ.get("DB_PORT", "5432"),
            dbname=os.environ.get("DB_NAME", "trend_crawler"),
            user=os.environ.get("DB_USER", "postgres"),
            password=os.environ.get("DB_PASSWORD", "postgres123")
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        raise

# Ensure users table exists
def init_user_db():
    """Initialize the user database."""
    global USERS_DB, POSTGRES_AVAILABLE
    USERS_DB = {}  # Initialize empty fallback dictionary

    if not POSTGRES_AVAILABLE:
        logger.warning("PostgreSQL not available, using in-memory user database")
        # Add a default admin user to in-memory DB with admin/admin credentials
        admin_password = "admin"
        USERS_DB[DEFAULT_ADMIN_USERNAME] = {
            "username": DEFAULT_ADMIN_USERNAME,
            "email": DEFAULT_ADMIN_EMAIL,
            "full_name": DEFAULT_ADMIN_FULLNAME,
            "disabled": False,
            "is_admin": True,
            "password_hash": pwd_context.hash(admin_password),
            "needs_password_change": ENFORCE_PASSWORD_CHANGE  # Flag to enforce password change on first login
        }
        logger.info(f"Created default admin user with username: {DEFAULT_ADMIN_USERNAME}, password: {admin_password}")
        return

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Create users table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            username VARCHAR(50) PRIMARY KEY,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name VARCHAR(100),
            disabled BOOLEAN DEFAULT FALSE,
            is_admin BOOLEAN DEFAULT FALSE,
            password_hash VARCHAR(200) NOT NULL,
            needs_password_change BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")

        # Check if admin user exists, create if not (but don't update existing password)
        cursor.execute("SELECT username FROM users WHERE username = %s", (DEFAULT_ADMIN_USERNAME,))
        admin_exists = cursor.fetchone()

        if not admin_exists:
            # Create admin user only if it doesn't exist
            admin_password = "admin"  # Default password for admin
            hashed_admin_password = pwd_context.hash(admin_password)

            cursor.execute('''
            INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash, needs_password_change)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ''', (
                DEFAULT_ADMIN_USERNAME,
                DEFAULT_ADMIN_EMAIL,
                DEFAULT_ADMIN_FULLNAME,
                False,
                True,
                hashed_admin_password,
                ENFORCE_PASSWORD_CHANGE
            ))
            logger.info(f"Created default admin user '{DEFAULT_ADMIN_USERNAME}' with password '{admin_password}'.")
        else:
            logger.info(f"Admin user '{DEFAULT_ADMIN_USERNAME}' already exists. Keeping existing password.")

        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")

        conn.commit()
        cursor.close()
        conn.close()
        logger.info("User database initialized")
    except Exception as e:
        logger.error(f"Error initializing user database: {e}")
        # Fallback to in-memory database if DB initialization fails
        logger.warning("Using in-memory user database as fallback")
        # Use admin/admin as default credentials
        admin_password = "admin"
        USERS_DB[DEFAULT_ADMIN_USERNAME] = {
            "username": DEFAULT_ADMIN_USERNAME,
            "email": DEFAULT_ADMIN_EMAIL,
            "full_name": DEFAULT_ADMIN_FULLNAME,
            "disabled": False,
            "is_admin": True,
            "password_hash": pwd_context.hash(admin_password),
            "needs_password_change": ENFORCE_PASSWORD_CHANGE  # Flag to enforce password change on first login
        }
        logger.info(f"Created default admin user with username: {DEFAULT_ADMIN_USERNAME}, password: {admin_password}")
        POSTGRES_AVAILABLE = False

# Password functions
def verify_password(plain_password, hashed_password):
    """Verify a password against a hash."""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except UnknownHashError:
        logger.warning(f"Unable to verify password for hash: {hashed_password[:6]}...: unknown hash format")
        return False

def get_password_hash(password):
    """Get password hash."""
    return pwd_context.hash(password)

# User functions
def get_user(username: str):
    """Get a user by username."""
    # First try SQLite database
    db_file = os.path.join(os.path.dirname(__file__), "scraper_metrics.db")
    
    try:
        import sqlite3
        with sqlite3.connect(db_file) as conn:
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users WHERE username = ?", 
                         (username,))
            
            user_record = cursor.fetchone()
            if user_record:
                user_dict = dict(user_record)
                return UserInDB(**user_dict)
    except Exception as e:
        logger.error("Error getting user from SQLite database: %s", e)
    
    # Fall back to PostgreSQL if SQLite fails and PostgreSQL is available
    if POSTGRES_AVAILABLE:
        try:
            conn = psycopg2.connect(
                host=os.environ.get("DB_HOST", "localhost"),
                port=os.environ.get("DB_PORT", "5432"),
                dbname=os.environ.get("DB_NAME", "scraper_metrics"),
                user=os.environ.get("DB_USER", "postgres"),
                password=os.environ.get("DB_PASSWORD", "postgres")
            )

            cursor = conn.cursor(cursor_factory=DictCursor)
            cursor.execute("SELECT * FROM users WHERE username = %s", 
                         (username,))

            user_record = cursor.fetchone()
            cursor.close()
            conn.close()

            if user_record:
                user_dict = dict(user_record)
                return UserInDB(**user_dict)
        except Exception as e:
            logger.error("Error getting user from PostgreSQL database: %s", e)
    
    # Final fallback to in-memory database
    user_data = USERS_DB.get(username)
    if user_data:
        return UserInDB(**user_data)
    return None

def authenticate_user(username: str, password: str):
    """Authenticate a user."""
    user = get_user(username)
    if not user:
        return False
    if not verify_password(password, user.password_hash):
        return False
    return user

# Token functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.datetime.now(datetime.timezone.utc) + expires_delta
    else:
        expire = datetime.datetime.now(datetime.timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# Authentication dependency
async def get_token_from_header_or_cookie(
    request: Request,
    token_from_header: Optional[str] = Depends(oauth2_scheme_optional) # Try header first
) -> Optional[str]:
    if token_from_header:
        return token_from_header

    token_from_cookie = request.cookies.get("access_token_cookie")
    return token_from_cookie

async def get_current_user(request: Request, token: Optional[str] = Depends(get_token_from_header_or_cookie)):
    """Get the current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"}, # Or potentially redirect to login via cookie mechanism
    )
    if token is None:
        raise credentials_exception

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if not isinstance(username, str) or username is None:
            raise credentials_exception

        # Store needs_password_change in request state
        needs_password_change = payload.get("needs_password_change", False) # Default to False if not in token
        request.state.needs_password_change = needs_password_change

        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(request: Request, current_user: User = Depends(get_current_user)):
    """Get the current active user."""
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")

    needs_password_change = getattr(request.state, "needs_password_change", False)

    # Define allowed paths when password change is needed
    allowed_paths_when_password_change_needed = [
        "/account",
        "/api/v1/users/change-password",
        "/login",
        "/api/v1/auth/token",
        "/api/v1/auth/me",
        "/logout" # Added logout path
    ]

    if needs_password_change and request.url.path not in allowed_paths_when_password_change_needed:
        # Also check if the request is for a static file, which should be allowed
        if not request.url.path.startswith("/static"):
            # Also check if the request is for the root path, which might redirect to login/dashboard
            if request.url.path == "/":
                # Allow root path, it will handle its own redirection logic
                pass
            else:
                return RedirectResponse(url="/account?prompt_change=true", status_code=status.HTTP_307_TEMPORARY_REDIRECT)

    return current_user

async def get_admin_user(request: Request, current_user: User = Depends(get_current_active_user)):
    """Get current user and verify they are an admin."""
    # Pass the request object to get_current_active_user if it's part of the dependency chain
    # However, Depends() handles this automatically if get_current_active_user is updated.
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized for admin actions"
        )
    return current_user

# Authentication dependency for HTML pages (redirects instead of exceptions)
async def get_current_user_for_html(request: Request, token: Optional[str] = Depends(get_token_from_header_or_cookie)) -> Optional[User]:
    """Get the current authenticated user for HTML pages. Returns None if not authenticated instead of raising exceptions."""
    if token is None:
        return None

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if not isinstance(username, str) or username is None:
            return None

        # Store needs_password_change in request state
        needs_password_change = payload.get("needs_password_change", False)
        request.state.needs_password_change = needs_password_change

        token_data = TokenData(username=username)
    except JWTError:
        return None

    user = get_user(token_data.username)
    if user is None:
        return None

    return user

async def get_current_active_user_for_html(request: Request, current_user: Optional[User] = Depends(get_current_user_for_html)) -> Optional[User]:
    """Get the current active user for HTML pages. Returns None if not authenticated or user is disabled."""
    if current_user is None:
        return None

    if current_user.disabled:
        return None

    return current_user


def is_browser_request(request: Request) -> bool:
    """Check if the request is coming from a browser (not an API call)."""
    accept_header = request.headers.get("accept", "")
    user_agent = request.headers.get("user-agent", "")

    # Check for common browser Accept headers
    return (
        "text/html" in accept_header or
        "application/xhtml+xml" in accept_header or
        "text/html,application/xhtml+xml" in accept_header.lower() or
        # Also check user agent for common browsers
        any(browser in user_agent.lower() for browser in ["mozilla", "chrome", "safari", "firefox", "edge"])
    )


async def get_current_active_user_or_redirect(
    request: Request,
    token: Optional[str] = Depends(get_token_from_header_or_cookie)
) -> Union[User, RedirectResponse]:
    """
    Get the current active user for dashboard pages.
    If not authenticated and it's a browser request, redirect to login.
    If not authenticated and it's an API request, raise 401.
    """
    try:
        # Try to get the user using the existing authentication logic
        current_user = await get_current_user(request, token)
        # Then apply the active user check with password change logic
        active_user = await get_current_active_user(request, current_user)
        return active_user
    except HTTPException as e:
        # If authentication failed, check if this is a browser request
        if e.status_code == 401 and is_browser_request(request):
            # For browser requests, redirect to login with return URL
            from urllib.parse import quote
            return_url = quote(str(request.url))
            login_url = f"/login?return_url={return_url}"
            # Return a proper RedirectResponse instead of raising HTTPException
            return RedirectResponse(
                url=login_url,
                status_code=status.HTTP_307_TEMPORARY_REDIRECT
            )
        else:
            # For API requests or other errors, re-raise the original exception
            raise e

# Create FastAPI app
app = FastAPI(
    title="Trend Crawler Dashboard",
    description="Web dashboard for the trend-crawler system",
    version="1.0.0"
)

# Add middleware for logging access attempts
@app.middleware("http")
async def log_access_attempts(request: Request, call_next):
    """Log all access attempts for security monitoring."""
    start_time = datetime.datetime.now()
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "Unknown")
    path = request.url.path
    method = request.method
    
    # Get user info if available
    user_info = "Anonymous"
    try:
        token = None
        if "Authorization" in request.headers:
            auth_header = request.headers["Authorization"]
            if auth_header.startswith("Bearer "):
                token = auth_header[7:]
        elif "access_token_cookie" in request.cookies:
            token = request.cookies.get("access_token_cookie")
        
        if token:
            try:
                payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
                username = payload.get("sub")
                if username:
                    user_info = f"User:{username}"
            except JWTError:
                user_info = "Invalid Token"
    except Exception:
        pass
    
    # Process the request
    response = await call_next(request)
    
    # Calculate response time
    process_time = (datetime.datetime.now() - start_time).total_seconds()
    
    # Log the access attempt
    security_logger.info(
        f"ACCESS - IP:{client_ip} - {user_info} - {method} {path} - "
        f"Status:{response.status_code} - Time:{process_time:.3f}s - "
        f"UA:{user_agent[:100]}"
    )
    
    return response

# Add exception handler for 404 errors (security: redirect to login instead of showing error)
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Handle 404 errors by redirecting to login for security."""
    client_ip = request.client.host
    path = request.url.path
    
    # Log the 404 attempt
    security_logger.warning(
        f"404_ATTEMPT - IP:{client_ip} - Path:{path} - "
        f"UA:{request.headers.get('user-agent', 'Unknown')[:100]}"
    )
    
    # Check if it's an API request (should return JSON)
    if path.startswith("/api/"):
        return JSONResponse(
            status_code=404,
            content={"detail": "Endpoint not found"}
        )
    
    # For web requests, redirect to login for security
    return RedirectResponse(url="/login", status_code=status.HTTP_307_TEMPORARY_REDIRECT)

# Add exception handler for 500 errors
@app.exception_handler(500)
async def internal_server_error_handler(request: Request, exc):
    """Handle 500 errors."""
    client_ip = request.client.host
    path = request.url.path
    
    # Log the error
    security_logger.error(
        f"500_ERROR - IP:{client_ip} - Path:{path} - Error:{str(exc)}"
    )
    
    # Check if it's an API request
    if path.startswith("/api/"):
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )
    
    # For web requests, redirect to login
    return RedirectResponse(url="/login", status_code=status.HTTP_307_TEMPORARY_REDIRECT)

# Create static directory if it doesn't exist
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)
css_dir = static_dir / "css"
css_dir.mkdir(exist_ok=True)
js_dir = static_dir / "js"
js_dir.mkdir(exist_ok=True)

# Create templates directory if it doesn't exist
templates_dir = Path("templates")
templates_dir.mkdir(exist_ok=True)

# Create templates
templates = Jinja2Templates(directory="templates")

# Add get_current_year function to template context
templates.env.globals["get_current_year"] = get_current_year

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Create router for API endpoints
api_router = APIRouter(prefix="/api/v1")

# Create router for authentication endpoints
auth_router = APIRouter(prefix="/auth")

# Create router for user management endpoints
user_router = APIRouter(prefix="/users")

# Create router for CAPTCHA endpoints
captcha_router = APIRouter(prefix="/captchas")

# Create router for integration test endpoints
integration_router = APIRouter(prefix="/integration")

# Create router for dashboard pages
dashboard_router = APIRouter()

# Original models
class ScraperStatsRequest(BaseModel):
    """Request model for scraper statistics."""
    scraper_name: str
    hours: int = Field(24, description="Number of hours to look back")

class ProxyStatsRequest(BaseModel):
    """Request model for proxy statistics."""
    proxy_id: Optional[str] = Field(None, description="Proxy ID (optional)")
    hours: int = Field(24, description="Number of hours to look back")

class AlertRequest(BaseModel):
    """Request model for sending alerts."""
    alert_type: str
    severity: str = Field(..., description="Alert severity (info, warning, error, critical)")
    message: str
    details: Optional[Dict[str, Any]] = Field(None, description="Additional alert details")

class ThresholdRequest(BaseModel):
    """Request model for setting alert thresholds."""
    metric: str
    threshold: float
    channel: Optional[str] = Field(None, description="Alert channel to use (optional)")

# Authentication endpoints
@auth_router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """Login and get access token."""
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if this user needs to change their password
    needs_password_change = False
    if POSTGRES_AVAILABLE:
        # Check the PostgreSQL database for the needs_password_change flag
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users WHERE username = %s", (user.username,))
            user_record = cursor.fetchone()
            cursor.close()
            conn.close()

            # Check if the column exists (in case of older DB schema)
            if user_record and 'needs_password_change' in user_record:
                needs_password_change = user_record['needs_password_change']
        except Exception as e:
            logger.error(f"Error checking password change status: {e}")
            # Fall back to in-memory check
            needs_password_change = USERS_DB.get(user.username, {}).get("needs_password_change", False)
    else:
        # Use in-memory database
        needs_password_change = USERS_DB.get(user.username, {}).get("needs_password_change", False)

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "needs_password_change": needs_password_change},
        expires_delta=access_token_expires
    )

    # If first login, include that in the response so frontend can redirect to change password
    response_content = {
        "access_token": access_token,
        "token_type": "bearer",
        "needs_password_change": needs_password_change
    }
    response = JSONResponse(content=response_content)
    response.set_cookie(
        key="access_token_cookie",
        value=access_token,
        httponly=True,
        path="/",
        samesite="lax",
        secure=False # Should be True in production if using HTTPS
    )
    return response

@auth_router.get("/me", response_model=User)
async def read_users_me(request: Request, current_user: User = Depends(get_current_active_user)):
    """Get current user information."""
    return current_user

# User management endpoints (admin only)
@user_router.post("/", response_model=User)
async def create_user(user: UserCreate, request: Request, current_user: User = Depends(get_admin_user)):
    """Create a new user."""
    if not POSTGRES_AVAILABLE:
        # Fallback to in-memory database
        if user.username in USERS_DB: # type: ignore
            raise HTTPException(status_code=400, detail="Username already registered")

        user_dict = user.model_dump()
        hashed_password = get_password_hash(user_dict.pop("password"))

        new_user_data = { # Renamed to avoid conflict with the 'user' parameter
            **user_dict,
            "disabled": False,
            "password_hash": hashed_password
        }

        USERS_DB[user.username] = new_user_data # type: ignore
        return User(**new_user_data) # type: ignore

    # Use PostgreSQL database
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=DictCursor)

        # Check if username already exists
        cursor.execute("SELECT * FROM users WHERE username = %s", (user.username,))
        if cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=400, detail="Username already registered")

        # Check if email already exists
        cursor.execute("SELECT * FROM users WHERE email = %s", (user.email,))
        if cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=400, detail="Email already registered")

        # Create new user
        user_dict = user.model_dump()
        hashed_password = get_password_hash(user_dict.pop("password"))

        cursor.execute("""
        INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash, needs_password_change)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        RETURNING *
        """, (
            user.username,
            user.email,
            user.full_name,
            False,
            user.is_admin,
            hashed_password,
            ENFORCE_PASSWORD_CHANGE  # Force new users to change password on first login
        ))

        new_user_record = cursor.fetchone()
        conn.commit()
        cursor.close()
        conn.close()

        if new_user_record:
            return User(**dict(new_user_record))
        else:
            raise HTTPException(status_code=500, detail="Failed to create user")
    except Exception as e:
        logger.error(f"Error creating user in database: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@user_router.get("/", response_model=List[User])
async def list_users(request: Request, current_user: User = Depends(get_admin_user)):
    """List all users."""
    if not POSTGRES_AVAILABLE:
        # Fallback to in-memory database
        return [User(**u) for u in USERS_DB.values()] # Renamed loop variable

    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=DictCursor)

        # Get all users
        cursor.execute("SELECT * FROM users")
        users = cursor.fetchall()

        cursor.close()
        conn.close()

        return [User(**dict(user)) for user in users]
    except Exception as e:
        logger.error(f"Error listing users from database: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@user_router.delete("/{username}")
async def delete_user(username: str, request: Request, current_user: User = Depends(get_admin_user)):
    """Delete a user."""
    if username == current_user.username:
        raise HTTPException(status_code=400, detail="Cannot delete your own account")

    if not POSTGRES_AVAILABLE:
        # Fallback to in-memory database
        if username not in USERS_DB: # type: ignore
            raise HTTPException(status_code=404, detail="User not found")

        del USERS_DB[username] # type: ignore
        return {"status": "success", "message": f"User {username} deleted"}

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if user exists
        cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
        if cursor.fetchone() is None:
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail="User not found")

        # Delete user
        cursor.execute("DELETE FROM users WHERE username = %s", (username,))
        conn.commit()
        cursor.close()
        conn.close()

        return {"status": "success", "message": f"User {username} deleted"}
    except Exception as e:
        logger.error(f"Error deleting user from database: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@user_router.post("/change-password")
async def change_password(
    request: Request, # Added request
    password_change: PasswordChange,
    current_user: User = Depends(get_current_active_user)
):
    """Change the password for the current user."""
    # The current_user already has the request object implicitly via Depends(get_current_active_user)
    # which itself depends on get_current_user(request: Request, ...)
    if POSTGRES_AVAILABLE:
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Verify current password
            cursor.execute("SELECT password_hash FROM users WHERE username = %s",
                          (current_user.username,))
            user_record = cursor.fetchone()
            if not user_record:
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail="User not found")

            password_hash = user_record[0] # type: ignore
            if not verify_password(password_change.current_password, password_hash): # type: ignore
                cursor.close()
                conn.close()
                raise HTTPException(status_code=400, detail="Current password is incorrect")

            # Update password and reset the needs_password_change flag
            cursor.execute("""
                UPDATE users
                SET password_hash = %s, needs_password_change = false, updated_at = CURRENT_TIMESTAMP
                WHERE username = %s
                """,
                (get_password_hash(password_change.new_password), current_user.username)
            )

            conn.commit()
            cursor.close()
            conn.close()

            # Update request state as password has been changed
            if hasattr(request.state, "needs_password_change"):
                # Generate new token
                access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
                new_access_token = create_access_token(
                    data={"sub": current_user.username, "needs_password_change": False},
                    expires_delta=access_token_expires
                )
                response_content_pg = {
                    "status": "success",
                    "message": "Password updated successfully",
                    "access_token": new_access_token,
                    "token_type": "bearer"
                }
                response_pg = JSONResponse(content=response_content_pg)
                response_pg.set_cookie(
                    key="access_token_cookie",
                    value=new_access_token,
                    httponly=True,
                    path="/",
                    samesite="lax",
                    secure=False # Should be True in production
                )
                return response_pg
        except Exception as e:
            logger.error(f"Error updating password in database: {e}")
            # Fall through to in-memory DB if database update fails

    # Fallback to in-memory user database
    user_data_fallback = USERS_DB.get(current_user.username) # Renamed variable
    if not user_data_fallback: # Renamed variable
        raise HTTPException(status_code=404, detail="User not found")

    # Verify current password
    if not verify_password(password_change.current_password, user_data_fallback["password_hash"]): # Renamed variable
        raise HTTPException(status_code=400, detail="Current password is incorrect")

    # Hash and set new password
    user_data_fallback["password_hash"] = get_password_hash(password_change.new_password) # Renamed variable

    # Reset the password change flag
    user_data_fallback["needs_password_change"] = False # Renamed variable

    # Update request state as password has been changed
    if hasattr(request.state, "needs_password_change"):
        request.state.needs_password_change = False

    # Generate new token for in-memory DB case
    access_token_expires_fallback = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    new_access_token_fallback = create_access_token(
        data={"sub": current_user.username, "needs_password_change": False},
        expires_delta=access_token_expires_fallback
    )
    response_content_fallback = {
        "status": "success",
        "message": "Password updated successfully",
        "access_token": new_access_token_fallback,
        "token_type": "bearer"
    }
    response_fallback = JSONResponse(content=response_content_fallback)
    response_fallback.set_cookie(
        key="access_token_cookie",
        value=new_access_token_fallback,
        httponly=True,
        path="/",
        samesite="lax",
        secure=False # Should be True in production
    )
    return response_fallback

@user_router.post("/profile", response_model=dict)
async def update_profile(
    profile_data: ProfileUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """Update user profile information."""
    if POSTGRES_AVAILABLE:
        try:
            # Check if email is already taken by another user
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute(
                "SELECT username FROM users WHERE email = %s AND username != %s",
                (profile_data.email, current_user.username)
            )
            existing_user = cursor.fetchone()

            if existing_user:
                cursor.close()
                conn.close()
                raise HTTPException(
                    status_code=400,
                    detail="Email address is already in use by another user"
                )

            # Update user profile
            cursor.execute(
                "UPDATE users SET email = %s, full_name = %s, updated_at = CURRENT_TIMESTAMP WHERE username = %s",
                (profile_data.email, profile_data.full_name, current_user.username)
            )
            conn.commit()

            # Get updated user data
            cursor.execute(
                "SELECT username, email, full_name, disabled, is_admin, created_at, updated_at FROM users WHERE username = %s",
                (current_user.username,)
            )
            updated_user = cursor.fetchone()

            cursor.close()
            conn.close()

            if updated_user:
                return {
                    "status": "success",
                    "message": "Profile updated successfully",
                    "user": {
                        "username": updated_user[0],
                        "email": updated_user[1],
                        "full_name": updated_user[2],
                        "disabled": updated_user[3],
                        "is_admin": updated_user[4],
                        "created_at": updated_user[5].isoformat() if updated_user[5] else None,
                        "updated_at": updated_user[6].isoformat() if updated_user[6] else None
                    }
                }
            else:
                raise HTTPException(status_code=404, detail="User not found after update")

        except Exception as e:
            logger.error(f"Database error updating profile: {e}")
            raise HTTPException(status_code=500, detail="Failed to update profile")
    else:
        # In-memory fallback
        if current_user.username not in USERS_DB:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if email is already taken by another user
        for username, user_data in USERS_DB.items():
            if username != current_user.username and user_data.get("email") == profile_data.email:
                raise HTTPException(
                    status_code=400,
                    detail="Email address is already in use by another user"
                )

        # Update user data
        USERS_DB[current_user.username]["email"] = profile_data.email
        if profile_data.full_name is not None:
            USERS_DB[current_user.username]["full_name"] = profile_data.full_name

        updated_user_data = USERS_DB[current_user.username]
        return {
            "status": "success",
            "message": "Profile updated successfully",
            "user": {
                "username": updated_user_data["username"],
                "email": updated_user_data["email"],
                "full_name": updated_user_data.get("full_name"),
                "disabled": updated_user_data.get("disabled", False),
                "is_admin": updated_user_data.get("is_admin", False)
            }
        }

@user_router.post("/{username}/profile", response_model=dict)
async def admin_update_user_profile(
    username: str,
    profile_data: ProfileUpdate,
    current_user: User = Depends(get_admin_user)
):
    """Update another user's profile information (admin only)."""
    if POSTGRES_AVAILABLE:
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if target user exists
            cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
            target_user = cursor.fetchone()

            if not target_user:
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail="User not found")

            target_user_id = target_user[0]

            # Check if email is already taken by another user
            cursor.execute(
                "SELECT id FROM users WHERE email = %s AND id != %s",
                (profile_data.email, target_user_id)
            )
            existing_user = cursor.fetchone()

            if existing_user:
                cursor.close()
                conn.close()
                raise HTTPException(
                    status_code=400,
                    detail="Email address is already in use by another user"
                )

            # Update user profile
            cursor.execute(
                "UPDATE users SET email = %s, full_name = %s, updated_at = CURRENT_TIMESTAMP WHERE username = %s",
                (profile_data.email, profile_data.full_name, username)
            )
            conn.commit()

            # Get updated user data
            cursor.execute(
                "SELECT username, email, full_name, disabled, is_admin, created_at, updated_at FROM users WHERE username = %s",
                (username,)
            )
            updated_user = cursor.fetchone()

            cursor.close()
            conn.close()

            if updated_user:
                return {
                    "status": "success",
                    "message": f"Profile for user {username} updated successfully",
                    "user": {
                        "username": updated_user[0],
                        "email": updated_user[1],
                        "full_name": updated_user[2],
                        "disabled": updated_user[3],
                        "is_admin": updated_user[4],
                        "created_at": updated_user[5].isoformat() if updated_user[5] else None,
                        "updated_at": updated_user[6].isoformat() if updated_user[6] else None
                    }
                }
            else:
                raise HTTPException(status_code=404, detail="User not found after update")

        except Exception as e:
            logger.error(f"Database error updating user profile: {e}")
            raise HTTPException(status_code=500, detail="Failed to update user profile")
    else:
        # In-memory fallback
        if username not in USERS_DB:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if email is already taken by another user
        for user_key, user_data in USERS_DB.items():
            if user_key != username and user_data.get("email") == profile_data.email:
                raise HTTPException(
                    status_code=400,
                    detail="Email address is already in use by another user"
                )

        # Update user data
        USERS_DB[username]["email"] = profile_data.email
        if profile_data.full_name is not None:
            USERS_DB[username]["full_name"] = profile_data.full_name

        updated_user_data = USERS_DB[username]
        return {
            "status": "success",
            "message": f"Profile for user {username} updated successfully",
            "user": {
                "username": updated_user_data["username"],
                "email": updated_user_data["email"],
                "full_name": updated_user_data.get("full_name"),
                "disabled": updated_user_data.get("disabled", False),
                "is_admin": updated_user_data.get("is_admin", False)
            }
        }

@user_router.post("/{username}/reset-password", response_model=dict)
async def reset_password(
    username: str,
    new_password: str = Form(...),
    current_user: User = Depends(get_admin_user)
):
    """Reset a user's password (admin only)."""
    if POSTGRES_AVAILABLE:
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if user exists
            cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            if cursor.fetchone() is None:
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail="User not found")

            # Set new password and enforce password change on next login
            cursor.execute("""
                UPDATE users
                SET password_hash = %s, needs_password_change = true, updated_at = CURRENT_TIMESTAMP
                WHERE username = %s
                """,
                (get_password_hash(new_password), username)
            )

            conn.commit()
            cursor.close()
            conn.close()

            return {"status": "success", "message": f"Password reset for user {username}"}
        except Exception as e:
            logger.error(f"Error resetting password in database: {e}")
            # Fall through to in-memory DB if database update fails

    # Fallback to in-memory user database
    if username not in USERS_DB: # type: ignore
        raise HTTPException(status_code=404, detail="User not found")

    # Hash and set new password
    USERS_DB[username]["password_hash"] = get_password_hash(new_password) # type: ignore
    # Set flag to force password change on next login
    USERS_DB[username]["needs_password_change"] = True # type: ignore

    return {"status": "success", "message": f"Password reset for user {username}"}

@user_router.post("/{username}/promote-admin", response_model=dict)
async def promote_user_admin(
    username: str,
    promote: bool = Form(...),
    current_user: User = Depends(get_admin_user)
):
    """Promote or demote a user's admin status (admin only)."""
    if username == current_user.username:
        raise HTTPException(status_code=400, detail="Cannot change your own admin status")

    if POSTGRES_AVAILABLE:
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if user exists
            cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            if cursor.fetchone() is None:
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail="User not found")

            # Update admin status
            cursor.execute("""
                UPDATE users
                SET is_admin = %s, updated_at = CURRENT_TIMESTAMP
                WHERE username = %s
                """,
                (promote, username)
            )

            conn.commit()
            cursor.close()
            conn.close()

            action = "promoted to" if promote else "demoted from"
            return {"status": "success", "message": f"User {username} {action} admin status"}
        except Exception as e:
            logger.error(f"Error updating admin status in database: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    else:
        # Fallback to in-memory user database
        if username not in USERS_DB: # type: ignore
            raise HTTPException(status_code=404, detail="User not found")

        # Update admin status
        USERS_DB[username]["is_admin"] = promote # type: ignore

        action = "promoted to" if promote else "demoted from"
        return {"status": "success", "message": f"User {username} {action} admin status"}

# Dashboard Pages
@dashboard_router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Login page."""
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "title": "Login"}
    )

# Registration disabled for private mode
# @dashboard_router.get("/register", response_class=HTMLResponse)
# async def register_page(request: Request):
#     """Register page."""
#     return templates.TemplateResponse(
#         "register.html",
#         {"request": request, "title": "Register"}
#     )

@dashboard_router.get("/", response_class=HTMLResponse)
async def root_redirect(request: Request, token: Optional[str] = Depends(get_token_from_header_or_cookie)):
    """Root path that redirects to appropriate page based on authentication status."""
    if not token:
        return RedirectResponse(url="/login")

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: Optional[str] = payload.get("sub")
        if username is None:
            return RedirectResponse(url="/login")

        user = get_user(username)
        if user is None or user.disabled:
            return RedirectResponse(url="/login")

        needs_password_change = payload.get("needs_password_change", False)

        if needs_password_change:
            if not hasattr(request.state, 'needs_password_change'):
                request.state.needs_password_change = needs_password_change
            if not hasattr(request.state, 'user'):
                request.state.user = user
            return RedirectResponse(url="/account?prompt_change=true", status_code=status.HTTP_307_TEMPORARY_REDIRECT)
        else:
            if not hasattr(request.state, 'needs_password_change'):
                request.state.needs_password_change = needs_password_change
            if not hasattr(request.state, 'user'):
                request.state.user = user
            return RedirectResponse(url="/dashboard", status_code=status.HTTP_307_TEMPORARY_REDIRECT)

    except JWTError:
        return RedirectResponse(url="/login")

@dashboard_router.get("/dashboard", response_class=HTMLResponse)
async def get_dashboard(request: Request, current_user: Union[User, RedirectResponse] = Depends(get_current_active_user_or_redirect)):
    """Main dashboard page."""
    # If we got a redirect response, return it
    if isinstance(current_user, RedirectResponse):
        return current_user

    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "title": "Trend Crawler Dashboard", "user": current_user}
    )

@dashboard_router.get("/scrapers", response_class=HTMLResponse)
async def get_scrapers_dashboard(request: Request, current_user: Union[User, RedirectResponse] = Depends(get_current_active_user_or_redirect)):
    """Scrapers dashboard page."""
    # If we got a redirect response, return it
    if isinstance(current_user, RedirectResponse):
        return current_user

    return templates.TemplateResponse(
        "scrapers.html",
        {"request": request, "title": "Scraper Performance", "user": current_user}
    )

@dashboard_router.get("/proxies", response_class=HTMLResponse)
async def get_proxies_dashboard(request: Request, current_user: Union[User, RedirectResponse] = Depends(get_current_active_user_or_redirect)):
    """Proxies dashboard page."""
    # If we got a redirect response, return it
    if isinstance(current_user, RedirectResponse):
        return current_user

    return templates.TemplateResponse(
        "proxies.html",
        {"request": request, "title": "Proxy Performance", "user": current_user}
    )

@dashboard_router.get("/captchas", response_class=HTMLResponse)
async def get_captchas_dashboard(request: Request, current_user: User = Depends(get_current_active_user_or_redirect)):
    """CAPTCHA dashboard page."""
    return templates.TemplateResponse(
        "captchas.html",
        {"request": request, "title": "CAPTCHA Analytics", "user": current_user}
    )

@dashboard_router.get("/alerts", response_class=HTMLResponse)
async def get_alerts_dashboard(request: Request, current_user: User = Depends(get_current_active_user_or_redirect)):
    """Alerts dashboard page."""
    return templates.TemplateResponse(
        "alerts.html",
        {"request": request, "title": "Alerts & Notifications", "user": current_user}
    )

@dashboard_router.get("/system", response_class=HTMLResponse)
async def get_system_dashboard(request: Request, current_user: User = Depends(get_current_active_user_or_redirect)):
    """System health dashboard page."""
    return templates.TemplateResponse(
        "system.html",
        {"request": request, "title": "System Health", "user": current_user}
    )

@dashboard_router.get("/integration-tests", response_class=HTMLResponse)
async def get_integration_tests_dashboard(request: Request, current_user: User = Depends(get_current_active_user_or_redirect)):
    """Integration tests dashboard page."""
    return templates.TemplateResponse(
        "integration_tests.html",
        {"request": request, "title": "Integration Tests", "user": current_user}
    )

@dashboard_router.get("/account", response_class=HTMLResponse)
async def get_account_page(request: Request, current_user: User = Depends(get_current_active_user_or_redirect)):
    """Account management page."""
    prompt_change_message = None
    if request.query_params.get("prompt_change") == "true":
        prompt_change_message = "For security reasons, you must change your default password before proceeding."

    return templates.TemplateResponse(
        "account.html",
        {
            "request": request,
            "title": "Account Settings",
            "user": current_user,
            "prompt_change_message": prompt_change_message
        }
    )

@dashboard_router.get("/test-navigation", response_class=HTMLResponse)
async def get_test_navigation_page(request: Request):
    """Test page for navigation click interception (no authentication required for testing)."""
    import os

    # Serve the test HTML file
    test_file_path = os.path.join(os.path.dirname(__file__), "test_navigation.html")
    if os.path.exists(test_file_path):
        return FileResponse(test_file_path, media_type="text/html")
    else:
        raise HTTPException(status_code=404, detail="Test file not found")

@dashboard_router.get("/user-management", response_class=HTMLResponse)
async def get_user_management_page(request: Request, current_user: User = Depends(get_admin_user)):
    """User management page (admin only)."""
    return templates.TemplateResponse(
        "user_management.html",
        {"request": request, "title": "User Management", "user": current_user}
    )

@dashboard_router.get("/logs", response_class=HTMLResponse)
async def get_logs_dashboard(request: Request, current_user: User = Depends(get_current_active_user_or_redirect)):
    """Logs viewing dashboard page (admin only for security)."""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    return templates.TemplateResponse(
        "logs.html",
        {"request": request, "title": "System Logs", "user": current_user}
    )

@api_router.get("/logs/security")
async def get_security_logs(current_user: User = Depends(get_admin_user), lines: int = 100):
    """Get recent security log entries (admin only)."""
    try:
        log_file = Path("dashboard.log")
        if not log_file.exists():
            return {"logs": [], "message": "No log file found"}
        
        # Read last N lines from log file
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        # Parse log entries
        logs = []
        for line in recent_lines:
            line = line.strip()
            if line and "SECURITY" in line:
                logs.append(line)
        
        return {"logs": logs, "total": len(logs)}
    except Exception as e:
        logger.error(f"Error reading security logs: {e}")
        return {"logs": [], "error": str(e)}

@api_router.get("/logs/access")
async def get_access_logs(current_user: User = Depends(get_admin_user), lines: int = 100):
    """Get recent access log entries (admin only)."""
    try:
        log_file = Path("dashboard.log")
        if not log_file.exists():
            return {"logs": [], "message": "No log file found"}
        
        # Read last N lines from log file
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        # Parse access log entries
        logs = []
        for line in recent_lines:
            line = line.strip()
            if line and "ACCESS" in line:
                logs.append(line)
        
        return {"logs": logs, "total": len(logs)}
    except Exception as e:
        logger.error(f"Error reading access logs: {e}")
        return {"logs": [], "error": str(e)}

@api_router.get("/logs/errors")
async def get_error_logs(current_user: User = Depends(get_admin_user), lines: int = 100):
    """Get recent error log entries (admin only)."""
    try:
        log_file = Path("dashboard.log")
        if not log_file.exists():
            return {"logs": [], "message": "No log file found"}
        
        # Read last N lines from log file
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        # Parse error log entries
        logs = []
        for line in recent_lines:
            line = line.strip()
            if line and ("ERROR" in line or "404_ATTEMPT" in line or "500_ERROR" in line):
                logs.append(line)
        
        return {"logs": logs, "total": len(logs)}
    except Exception as e:
        logger.error(f"Error reading error logs: {e}")
        return {"logs": [], "error": str(e)}

@dashboard_router.get("/logout", response_class=RedirectResponse)
async def logout_and_redirect(request: Request):
    """Logout and redirect to login page."""
    response = RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    response.delete_cookie(key="access_token_cookie", path="/")
    return response

# System API endpoints
@api_router.get("/system/info")
async def get_system_info():
    """Get system information including CPU, memory, disk usage, etc.
    Ensures a consistent dictionary structure is returned.
    """
    # Import platform here to avoid potential import issues at the top level
    # if it's only used in this function when psutil is not available.
    import platform

    # Define the default structure with "N/A" values
    system_info = {
        "hostname": "N/A",
        "cpu_usage": "N/A",
        "memory_usage": "N/A",
        "disk_usage": "N/A",
        "os_info": "N/A",
        "kernel": "N/A",
        "cpu_info": "N/A",
        "python_version": platform.python_version(), # Python version is always available
        "uptime": "N/A",
        "load_avg": "N/A"
    }

    if PSUTIL_AVAILABLE:
        try:
            import psutil
            import socket

            # Get hostname
            system_info["hostname"] = socket.gethostname()

            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            system_info["cpu_usage"] = f"{cpu_percent}%"

            # Get memory usage
            memory = psutil.virtual_memory()
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)
            system_info["memory_usage"] = f"{memory_used_gb:.1f}GB / {memory_total_gb:.1f}GB ({memory.percent}%)"

            # Get disk usage
            disk = psutil.disk_usage('/')
            disk_used_gb = disk.used / (1024**3)
            disk_total_gb = disk.total / (1024**3)
            disk_percent = (disk.used / disk.total) * 100
            system_info["disk_usage"] = f"{disk_used_gb:.1f}GB / {disk_total_gb:.1f}GB ({disk_percent:.1f}%)"

            # Get OS info
            system_info["os_info"] = f"{platform.system()} {platform.release()}"

            # Get kernel version
            system_info["kernel"] = platform.version()

            # Get CPU info
            system_info["cpu_info"] = f"{psutil.cpu_count()} cores"

            # Get uptime
            boot_time = psutil.boot_time()
            uptime_seconds = datetime.datetime.now().timestamp() - boot_time
            uptime_hours = uptime_seconds / 3600
            system_info["uptime"] = f"{uptime_hours:.1f} hours"

            # Get load average (Unix-like systems only)
            try:
                load_avg = psutil.getloadavg()
                system_info["load_avg"] = f"{load_avg[0]:.2f}, {load_avg[1]:.2f}, {load_avg[2]:.2f}"
            except (AttributeError, OSError):
                system_info["load_avg"] = "N/A (Windows)"

        except Exception as e:
            logger.error(f"Error getting system info with psutil: {e}")
            # Keep the default "N/A" values if psutil fails
    else:
        # If psutil is not available, try to get basic info
        try:
            import socket
            system_info["hostname"] = socket.gethostname()
            system_info["os_info"] = f"{platform.system()} {platform.release()}"
            system_info["kernel"] = platform.version()
        except Exception as e:
            logger.error(f"Error getting basic system info: {e}")

    return system_info

@api_router.get("/system/events")
async def get_system_events(request: Request, limit: int = 20):
    """Get recent system events."""
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            events_data = monitor.get_system_events(limit)
            if events_data:
                # Format monitoring data to match frontend expectations
                formatted_events = []
                for event in events_data:
                    # Check if it's already in the correct format (time, type, message)
                    if 'time' in event and 'type' in event and 'message' in event:
                        formatted_events.append(event)
                    else:
                        # Convert from monitoring format to frontend format
                        formatted_events.append({
                            'time': event.get('timestamp', datetime.datetime.now().isoformat()),
                            'type': event.get('level', 'info'),
                            'message': event.get('message', str(event))
                        })
                return formatted_events

        # Fallback to sample events if monitoring is not available
        sample_events = [
            {
                "time": datetime.datetime.now().isoformat(),
                "type": "info",
                "message": "System monitoring started"
            },
            {
                "time": (datetime.datetime.now() - timedelta(minutes=5)).isoformat(),
                "type": "info",
                "message": "Database connection established"
            },
            {
                "time": (datetime.datetime.now() - timedelta(minutes=10)).isoformat(),
                "type": "warning",
                "message": "High CPU usage detected"
            }
        ]
        return sample_events[:limit]

    except Exception as e:
        logger.error(f"Error getting system events: {e}")
        return []

@api_router.get("/system/api-health")
async def get_api_health():
    """Get API health metrics."""
    try:
        if MONITORING_AVAILABLE:
            health_data = monitor.get_api_health()
            if health_data:
                return health_data

        # Fallback health data
        return {
            "uptime": "99.9%",
            "success_rate": "98.5%",
            "requests_per_minute": "45",
            "error_rate": "1.5%"
        }
    except Exception as e:
        logger.error(f"Error getting API health: {e}")
        return {
            "uptime": "N/A",
            "success_rate": "N/A",
            "requests_per_minute": "N/A",
            "error_rate": "N/A"
        }

@api_router.get("/system/db-performance")
async def get_db_performance(hours: int = 1):
    """Get database performance metrics."""
    try:
        if MONITORING_AVAILABLE:
            db_data = monitor.get_db_performance(hours)
            if db_data:
                return db_data

        # Fallback database performance data
        return {
            "queries_per_minute": "25",
            "success_rate": "99.2%"
        }
    except Exception as e:
        logger.error(f"Error getting DB performance: {e}")
        return {
            "queries_per_minute": "N/A",
            "success_rate": "N/A"
        }

@api_router.get("/stats/scrapers")
async def get_scraper_stats(scraper_name: str = "all", hours: int = 24):
    """Get scraper statistics."""
    try:
        if MONITORING_AVAILABLE:
            stats = monitor.get_scraper_stats(scraper_name, hours)
            if stats:
                return stats

        # Fallback scraper stats
        return {
            "total_requests": 1250,
            "successful_requests": 1225,
            "failed_requests": 25,
            "success_rate": 98.0,
            "average_response_time": 1.2,
            "captcha_encounters": 15,
            "proxy_failures": 8,
            "scrapers": {
                "twitter": {"requests": 500, "success_rate": 98.5},
                "instagram": {"requests": 400, "success_rate": 97.8},
                "reddit": {"requests": 350, "success_rate": 98.9}
            }
        }
    except Exception as e:
        logger.error(f"Error getting scraper stats: {e}")
        return {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "success_rate": 0,
            "average_response_time": 0,
            "captcha_encounters": 0,
            "proxy_failures": 0,
            "scrapers": {}
        }


@api_router.get("/stats/proxies")
async def get_proxy_stats(hours: int = 24):
    """Get proxy statistics."""
    try:
        if MONITORING_AVAILABLE:
            stats = monitor.get_proxy_stats(hours)
            if stats:
                return stats

        # Fallback proxy stats
        return {
            "total_proxies": 150,
            "active_proxies": 142,
            "failed_proxies": 8,
            "average_response_time": 2.1,
            "success_rate": 94.7,
            "by_location": {
                "us": {"count": 60, "success_rate": 95.2},
                "eu": {"count": 45, "success_rate": 94.1},
                "asia": {"count": 35, "success_rate": 94.8}
            },
            "by_type": {
                "residential": {"count": 80, "success_rate": 96.5},
                "datacenter": {"count": 70, "success_rate": 92.8}
            }
        }
    except Exception as e:
        logger.error(f"Error getting proxy stats: {e}")
        return {
            "total_proxies": 0,
            "active_proxies": 0,
            "failed_proxies": 0,
            "average_response_time": 0,
            "success_rate": 0,
            "by_location": {},
            "by_type": {}
        }


@api_router.get("/stats/captchas")
async def get_captcha_stats(hours: int = 24):
    """Get CAPTCHA statistics."""
    try:
        if CAPTCHA_SOLVER_AVAILABLE:
            stats = captcha_solver.get_detection_stats()
            if stats:
                return stats

        # Fallback CAPTCHA stats
        return get_fallback_detection_stats()
    except Exception as e:
        logger.error(f"Error getting CAPTCHA stats: {e}")
        return get_fallback_detection_stats()


@api_router.get("/captchas/stats")
async def get_captcha_stats_alt(hours: int = 24):
    """Get CAPTCHA statistics (alternate endpoint for compatibility)."""
    return await get_captcha_stats(hours)

# Add routers to app
api_router.include_router(auth_router)
api_router.include_router(user_router)
api_router.include_router(captcha_router)
api_router.include_router(integration_router)
app.include_router(api_router)
app.include_router(dashboard_router)

if __name__ == "__main__":
    import uvicorn
    import sys

    # Initialize user database
    init_user_db()

    # Parse port from command line args
    port = 8001
    if len(sys.argv) > 1:
        for i, arg in enumerate(sys.argv):
            if arg == "--port" and i + 1 < len(sys.argv):
                port = int(sys.argv[i + 1])
                break

    # Run the application
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_level="info"
    )
